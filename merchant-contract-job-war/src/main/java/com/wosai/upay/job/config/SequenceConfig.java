package com.wosai.upay.job.config;

import com.alibaba.fastjson.JSON;
import com.wosai.upay.job.enume.SequenceNamespaceEnum;
import com.wosai.upay.job.refactor.dao.SequenceDAO;
import com.wosai.upay.job.refactor.model.entity.SequenceDO;
import com.wosai.upay.merchant.contract.exception.ContractBizException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.locks.ReentrantLock;

/**
 * <AUTHOR>
 * @date 2023/7/2
 */
@Slf4j
@Component
public class SequenceConfig {

    private static final ConcurrentMap<String, Pair<AtomicLong, Long>> SERIAL_NO_MAP
            = new ConcurrentHashMap<>();

    private static final ReentrantLock LOCK = new ReentrantLock();


    @Resource
    private SequenceDAO sequenceDAO;
    @Resource
    private TransactionTemplate transactionTemplate;
    @Resource
    @Qualifier("requiresNewTransactionTemplate")
    private TransactionTemplate requiresNewTransactionTemplate;


    /**
     * 生成序列
     *
     * @param namespace
     * @return
     */
    public long genSequence(String namespace) {
        Pair<AtomicLong, Long> pair = getNotNullPair(namespace);
        AtomicLong sequenceGenerator = pair.getLeft();
        Long stageMax = pair.getRight();
        long serialNo = sequenceGenerator.getAndIncrement();
        if (serialNo >= stageMax) {
            LOCK.lock(); // 获取锁
            try {
                pair = getNotNullPair(namespace);
                sequenceGenerator = pair.getLeft();
                stageMax = pair.getRight();
                serialNo = sequenceGenerator.getAndIncrement();
                if (serialNo < stageMax) {
                    return serialNo;
                }

                return load(namespace);
            } finally {
                LOCK.unlock(); // 确保释放锁
            }
        }
        return serialNo;
    }

    private Pair<AtomicLong, Long> getNotNullPair(String namespace) {
        Pair<AtomicLong, Long> pair = SERIAL_NO_MAP.get(namespace);
        if (Objects.isNull(pair)) {
            throw new ContractBizException("序列生成器不存在");
        }
        return pair;
    }

    /**
     * 这里一定要新建事务会话，不然和外层方法共用一个事务会话
     * 外层回滚，序列号的更新也一并回滚了，就会造成id重复
     * 批次跨度越大凉的越透
     *
     * @param namespace
     * @return
     */
    private Long load(String namespace) {
        return requiresNewTransactionTemplate.execute(status -> {
            //查询序列
            Optional<SequenceDO> sequenceDO = sequenceDAO.selectForUpdate(namespace);
            if (!sequenceDO.isPresent()) {
                throw new ContractBizException("序列生成器不存在");
            }
            SequenceDO sequence = sequenceDO.get();
            log.debug("[序列号生成器]>>>>>>加载配置: {}", JSON.toJSONString(sequence));

            long serialNoBase = sequence.getSerialNoBase();
            long newSerialNoBase = serialNoBase + sequence.getBatchSize();
            AtomicLong newSequenceGenerator = new AtomicLong(serialNoBase);
            long serialNo = newSequenceGenerator.getAndIncrement();
            Pair<AtomicLong, Long> newSequenceGeneratorPair
                    = Pair.of(newSequenceGenerator
                    , newSerialNoBase);

            //更新序列种子
            sequence.setMtime(LocalDateTime.now());
            sequence.setSerialNoBase(newSerialNoBase);
            sequenceDAO.update(sequence);

            //缓存新的序列生成器
            SERIAL_NO_MAP.put(namespace, newSequenceGeneratorPair);
            log.debug("[序列号生成器]>>>>>>更新后容器: {}, 更新后返回{}", SERIAL_NO_MAP, serialNo);
            return serialNo;
        });
    }

    @PostConstruct
    private void init() {
        SequenceNamespaceEnum[] sequenceNamespaceEnums = SequenceNamespaceEnum.values();
        for (SequenceNamespaceEnum sequenceNamespaceEnum : sequenceNamespaceEnums) {
            transactionTemplate.executeWithoutResult(transactionStatus -> {
                //查询序列
                Optional<SequenceDO> optionalSequenceDO = sequenceDAO
                        .selectForUpdate(sequenceNamespaceEnum.getNamespace());
                if (!optionalSequenceDO.isPresent()) {
                    throw new ContractBizException("序列生成器不存在");
                }
                SequenceDO sequenceDO = optionalSequenceDO.get();

                long serialNoBase = sequenceDO.getSerialNoBase();
                long newSerialNoBase = serialNoBase + sequenceDO.getBatchSize();
                AtomicLong newSequenceGenerator = new AtomicLong(serialNoBase);
                Pair<AtomicLong, Long> newSequenceGeneratorPair
                        = Pair.of(newSequenceGenerator
                        , newSerialNoBase);

                //更新序列种子
                sequenceDO.setMtime(LocalDateTime.now());
                sequenceDO.setSerialNoBase(newSerialNoBase);
                sequenceDAO.update(sequenceDO);

                //缓存新的序列生成器
                SERIAL_NO_MAP.put(sequenceNamespaceEnum.getNamespace(), newSequenceGeneratorPair);

                log.debug("[序列号生成器]>>>>>>初始化容器: {}", SERIAL_NO_MAP);
            });
        }
    }
}
