package com.wosai.upay.job.refactor.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.Version;
import com.wosai.upay.job.enume.tonglianV2.PayBizApplyDetailStatusEnum;
import com.wosai.upay.job.enume.tonglianV2.PayBizApplyStatusEnum;
import com.wosai.upay.job.refactor.model.bo.PayBizApplyExtraBO;
import com.wosai.upay.job.refactor.model.bo.PayBizApplyFlowInfoBO;
import com.wosai.upay.job.refactor.model.bo.PayBizApplyFlowNodeBO;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;
import java.util.Objects;
import java.util.Optional;

/**
 * 支付业务开通申请单实体对象
 *
 * <AUTHOR>
@TableName("pay_biz_apply")
@Data
@Slf4j
public class PayBizApplyDO {

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 商户号
     */
    @TableField(value = "merchant_sn")
    private String merchantSn;

    /**
     * 支付业务编码
     */
    @TableField(value = "dev_code")
    private String devCode;

    /**
     * 主状态：1开通中 2开通失败 3开通成功
     */
    @TableField(value = "status")
    private Integer status;

    /**
     * 详细状态编码
     */
    @TableField(value = "detail_status")
    private Integer detailStatus;

    /**
     * 详细状态中文描述，冗余存储，方便排查
     */
    @TableField(value = "detail_status_desc")
    private String detailStatusDesc;

    /**
     * 开通结果（json/text）
     */
    @TableField(value = "result")
    private String result;

    /**
     * 提交报文
     */
    @TableField(value = "form_body")
    private String formBody;

    /**
     * 扩展信息
     */
    @TableField(value = "extra")
    private String extra;

    /**
     * 全流程节点及状态，例：[{"desc":"进件","status":"success"},...]
     */
    @TableField(value = "flow_info")
    private String flowInfo;

    /**
     * 创建时间
     */
    @TableField(value = "ctime")
    private LocalDateTime ctime;

    /**
     * 更新时间
     */
    @TableField(value = "mtime")
    private LocalDateTime mtime;

    /**
     * 调度优先级时间，越小越优先
     */
    @TableField(value = "priority")
    private LocalDateTime priority;

    /**
     * 乐观锁版本号
     */
    @Version
    @TableField(value = "version")
    private Integer version;

    // ==================== 充血模型：业务方法 ====================

    /**
     * 更新申请状态
     *
     * @param status           主状态
     * @param detailStatus     详细状态
     * @param detailStatusDesc 详细状态描述
     */
    public void updateStatus(Integer status, Integer detailStatus, String detailStatusDesc) {
        this.status = status;
        this.detailStatus = detailStatus;
        this.detailStatusDesc = detailStatusDesc;
        touchMtime();
    }

    /**
     * 更新开通结果
     *
     * @param result 开通结果
     */
    public void updateResult(String result) {
        this.result = result;
        touchMtime();
    }

    /**
     * 更新流程信息
     *
     * @param flowInfo 流程信息
     */
    public void updateFlowInfo(String flowInfo) {
        this.flowInfo = flowInfo;
        touchMtime();
    }

    /**
     * 更新调度优先级
     *
     * @param priority 优先级时间
     */
    public void updatePriority(LocalDateTime priority) {
        this.priority = priority;
        touchMtime();
    }

    /**
     * 创建新的支付业务申请单
     *
     * @param merchantSn 商户号
     * @param devCode    支付业务编码
     * @param formBody   提交报文
     * @param flowInfoBO 流程信息BO
     * @return 新的申请单实例
     */
    public static PayBizApplyDO createNewApply(String merchantSn, String devCode, String formBody, Long taskId, PayBizApplyFlowInfoBO flowInfoBO) {
        PayBizApplyDO apply = new PayBizApplyDO();
        apply.merchantSn = merchantSn;
        apply.devCode = devCode;
        apply.status = PayBizApplyStatusEnum.PROCESSING.getCode(); // 开通中
        apply.detailStatus = PayBizApplyDetailStatusEnum.CONTRACT_AUDITING.getCode(); // 进件审核中
        apply.detailStatusDesc = PayBizApplyDetailStatusEnum.CONTRACT_AUDITING.getDisplayText();
        apply.formBody = formBody;
        apply.flowInfo = flowInfoBO != null ? flowInfoBO.toJsonString() : PayBizApplyFlowInfoBO.createDefaultFlow().toJsonString();
        apply.extra = PayBizApplyExtraBO.createWithTaskId(taskId).toJsonString();
        apply.ctime = LocalDateTime.now();
        apply.priority = LocalDateTime.now();
        apply.version = 1;
        return apply;
    }

    private void touchMtime() {
        this.mtime = LocalDateTime.now();
    }

    /**
     * 判断是否为开通中状态
     *
     * @return true if 开通中
     */
    public boolean isProcessing() {
        return PayBizApplyStatusEnum.isProcessing(this.status);
    }

    /**
     * 判断是否为开通成功状态
     *
     * @return true if 开通成功
     */
    public boolean isSuccess() {
        return PayBizApplyStatusEnum.isSuccess(this.status);
    }

    /**
     * 判断是否为开通失败状态
     *
     * @return true if 开通失败
     */
    public boolean isFailed() {
        return PayBizApplyStatusEnum.isFailed(this.status);
    }

    /**
     * 获取主状态枚举
     *
     * @return 主状态枚举
     */
    public PayBizApplyStatusEnum getStatusEnum() {
        return PayBizApplyStatusEnum.getByCode(this.status);
    }

    /**
     * 获取详细状态枚举
     *
     * @return 详细状态枚举
     */
    public PayBizApplyDetailStatusEnum getDetailStatusEnum() {
        return PayBizApplyDetailStatusEnum.getByCode(this.detailStatus);
    }

    /**
     * 判断是否为终态
     *
     * @return true-终态，false-非终态
     */
    public boolean isFinalState() {
        return PayBizApplyStatusEnum.isFinalState(this.status);
    }

    /**
     * 判断详细状态是否为审核中
     *
     * @return true-审核中，false-非审核中
     */
    public boolean isDetailAuditing() {
        return PayBizApplyDetailStatusEnum.isAuditing(this.detailStatus);
    }

    /**
     * 判断详细状态是否为签约相关
     *
     * @return true-签约相关，false-非签约相关
     */
    public boolean isDetailSignRelated() {
        return PayBizApplyDetailStatusEnum.isSignRelated(this.detailStatus);
    }

    /**
     * 判断详细状态是否为等待状态
     *
     * @return true-等待状态，false-非等待状态
     */
    public boolean isDetailWaiting() {
        return PayBizApplyDetailStatusEnum.isWaitingStatus(this.detailStatus);
    }

    /**
     * 判断是否可以设置启用
     *
     * @return true-可以设置启用，false-不可以设置启用
     */
    public boolean canSetEnabled() {
        return PayBizApplyDetailStatusEnum.WAITING_EFFECTIVE.getCode().equals(this.detailStatus) || PayBizApplyDetailStatusEnum.ENABLE_FAILED.getCode().equals(this.detailStatus);
    }

    public boolean isDetailWaitingEffective() {
        return PayBizApplyDetailStatusEnum.WAITING_EFFECTIVE.getCode().equals(this.detailStatus);
    }

    public boolean isDetailEnabling() {
        return PayBizApplyDetailStatusEnum.ENABLING.getCode().equals(this.detailStatus);
    }

    public boolean isDetailContractAuditing() {
        return PayBizApplyDetailStatusEnum.CONTRACT_AUDITING.getCode().equals(this.detailStatus);
    }

    public boolean isDetailWaitingSign() {
        return PayBizApplyDetailStatusEnum.WAITING_LEGAL_SIGN.getCode().equals(this.detailStatus) || PayBizApplyDetailStatusEnum.WAITING_SETTLEMENT_SIGN.getCode().equals(this.detailStatus);
    }

    public boolean isDetailComplianceReject() {
        return PayBizApplyDetailStatusEnum.COMPLIANCE_SUPPLEMENT_REJECTED.getCode().equals(this.detailStatus);
    }

    public boolean isDetailRiskControlReject() {
        return PayBizApplyDetailStatusEnum.RISK_CONTROL_REJECTED.getCode().equals(this.detailStatus);
    }

    public boolean isSignUrlExpired() {
        return PayBizApplyDetailStatusEnum.LEGAL_SIGN_EXPIRED.getCode().equals(this.detailStatus) || PayBizApplyDetailStatusEnum.SETTLEMENT_SIGN_EXPIRED.getCode().equals(this.detailStatus);
    }

    public boolean isEnabledFailed() {
        return PayBizApplyDetailStatusEnum.ENABLE_FAILED.getCode().equals(this.detailStatus);
    }

    public boolean isInEnablingStage() {
        return PayBizApplyDetailStatusEnum.ENABLING.getCode().equals(this.detailStatus) || PayBizApplyDetailStatusEnum.WAITING_EFFECTIVE.getCode().equals(this.detailStatus) || PayBizApplyDetailStatusEnum.ENABLE_FAILED.getCode().equals(this.detailStatus);
    }

    public boolean isInSigningOrExpiredStatus() {
        return PayBizApplyDetailStatusEnum.WAITING_LEGAL_SIGN.getCode().equals(detailStatus) ||
                PayBizApplyDetailStatusEnum.WAITING_SETTLEMENT_SIGN.getCode().equals(detailStatus) ||
                PayBizApplyDetailStatusEnum.LEGAL_SIGN_EXPIRED.getCode().equals(detailStatus) ||
                PayBizApplyDetailStatusEnum.SETTLEMENT_SIGN_EXPIRED.getCode().equals(detailStatus);
    }

    public boolean isDetailRiskControlAuditing() {
        return PayBizApplyDetailStatusEnum.RISK_CONTROL_AUDITING.getCode().equals(this.detailStatus);
    }

    /**
     * 获取流程信息BO
     *
     * @return 流程信息BO
     */
    public PayBizApplyFlowInfoBO getFlowInfoBO() {
        return PayBizApplyFlowInfoBO.fromJsonString(this.flowInfo);
    }

    /**
     * 设置流程信息BO
     *
     * @param flowInfoBO 流程信息BO
     */
    public void setFlowInfoBO(PayBizApplyFlowInfoBO flowInfoBO) {
        this.flowInfo = flowInfoBO != null ? flowInfoBO.toJsonString() : null;
        touchMtime();
    }

    /**
     * 标记指定状态的流程节点为完成
     *
     * @param detailStatus 详细状态码
     * @param finishTime   完成时间
     * @return true-标记成功，false-节点不存在
     */
    public boolean markFlowNodeAsFinished(Integer detailStatus, LocalDateTime finishTime) {
        PayBizApplyFlowInfoBO flowInfoBO = getFlowInfoBO();
        boolean success = flowInfoBO.markNodeAsSuccess(detailStatus, finishTime);
        if (success) {
            setFlowInfoBO(flowInfoBO);
        }
        return success;
    }

    /**
     * 标记指定状态的流程节点为完成（使用当前时间）
     *
     * @param detailStatus 详细状态码
     * @return true-标记成功，false-节点不存在
     */
    public boolean markFlowNodeAsFinished(Integer detailStatus) {
        return markFlowNodeAsFinished(detailStatus, LocalDateTime.now());
    }

    /**
     * 标记当前详细状态对应的流程节点为完成
     *
     * @param finishTime 完成时间
     * @return true-标记成功，false-节点不存在
     */
    public boolean markCurrentFlowNodeAsFinished(LocalDateTime finishTime) {
        return markFlowNodeAsFinished(this.detailStatus, finishTime);
    }

    /**
     * 标记当前详细状态对应的流程节点为完成（使用当前时间）
     *
     * @return true-标记成功，false-节点不存在
     */
    public boolean markCurrentFlowNodeAsFinished() {
        return markCurrentFlowNodeAsFinished(LocalDateTime.now());
    }

    /**
     * 获取流程完成进度（百分比）
     *
     * @return 完成进度，0-100
     */
    public int getFlowProgress() {
        return getFlowInfoBO().getProgress();
    }

    /**
     * 判断流程是否全部完成
     *
     * @return true-全部完成，false-还有未完成的节点
     */
    public boolean isFlowAllFinished() {
        return getFlowInfoBO().isAllFinished();
    }

    /**
     * 获取任务ID
     *
     * @return 任务ID
     */
    public Long getTaskId() {
        PayBizApplyExtraBO extraBO = PayBizApplyExtraBO.fromJsonString(this.extra);
        return extraBO != null ? extraBO.getTaskId() : null;
    }

    public String getChangeAcquirerApplyId() {
        PayBizApplyExtraBO extraBO = PayBizApplyExtraBO.fromJsonString(this.extra);
        return extraBO != null ? extraBO.getChangeAcquirerApplyId() : null;
    }

    public Long getSignUrlTime() {
        PayBizApplyExtraBO extraBO = PayBizApplyExtraBO.fromJsonString(this.extra);
        if (extraBO == null) {
            return null;
        }
        if (PayBizApplyDetailStatusEnum.WAITING_LEGAL_SIGN.getCode().equals(detailStatus)) {
            return extraBO.getLegalSignUrlTime();
        } else if (PayBizApplyDetailStatusEnum.WAITING_SETTLEMENT_SIGN.getCode().equals(detailStatus)) {
            return extraBO.getSettlementSignUrlTime();
        }
        return null;
    }

    public String getHandleDetail() {
        PayBizApplyExtraBO extraBO = PayBizApplyExtraBO.fromJsonString(this.extra);
        if (extraBO == null) {
            return null;
        }
        return extraBO.getHandleDetail();
    }



    /**
     * 回退到进件中状态
     * 进件审核失败 -> 回退到进件审核中
     *
     * @return true-回退成功，false-无需回退
     */
    public boolean rollBackToContracting() {
        if (PayBizApplyDetailStatusEnum.CONTRACT_AUDIT_FAILED.getCode().equals(this.detailStatus)) {
            // 进件审核失败 -> 回退到进件审核中
            updateStatus(PayBizApplyStatusEnum.PROCESSING.getCode(),
                    PayBizApplyDetailStatusEnum.CONTRACT_AUDITING.getCode(),
                    PayBizApplyDetailStatusEnum.CONTRACT_AUDITING.getDesc());

            // 更新流程状态
            PayBizApplyFlowInfoBO flowInfo = getFlowInfoBO();
            flowInfo.markNodeAsInProgress(PayBizApplyDetailStatusEnum.CONTRACT_AUDITING.getCode());
            setFlowInfoBO(flowInfo);
            return true;
        }
        return false; // 无需回退
    }

    /**
     * 回退到待签约状态
     * 法人签约失败 -> 回退到待法人签约
     * 结算人签约失败 -> 回退到待结算人签约
     *
     * @return true-回退成功，false-无需回退
     */
    public boolean rollBackToSigning() {
        if (PayBizApplyDetailStatusEnum.LEGAL_SIGN_FAILED.getCode().equals(this.detailStatus)) {
            // 法人签约失败 -> 回退到待法人签约
            updateStatus(PayBizApplyStatusEnum.PROCESSING.getCode(),
                    PayBizApplyDetailStatusEnum.WAITING_LEGAL_SIGN.getCode(),
                    PayBizApplyDetailStatusEnum.WAITING_LEGAL_SIGN.getDesc());

            // 更新流程状态
            PayBizApplyFlowInfoBO flowInfo = getFlowInfoBO();
            flowInfo.markNodeAsInProgress(PayBizApplyDetailStatusEnum.WAITING_LEGAL_SIGN.getCode());
            setFlowInfoBO(flowInfo);
            return true;

        } else if (PayBizApplyDetailStatusEnum.SETTLEMENT_SIGN_FAILED.getCode().equals(this.detailStatus)) {
            // 结算人签约失败 -> 回退到待结算人签约
            updateStatus(PayBizApplyStatusEnum.PROCESSING.getCode(),
                    PayBizApplyDetailStatusEnum.WAITING_SETTLEMENT_SIGN.getCode(),
                    PayBizApplyDetailStatusEnum.WAITING_SETTLEMENT_SIGN.getDesc());

            // 更新流程状态
            PayBizApplyFlowInfoBO flowInfo = getFlowInfoBO();
            flowInfo.markNodeAsInProgress(PayBizApplyDetailStatusEnum.WAITING_SETTLEMENT_SIGN.getCode());
            setFlowInfoBO(flowInfo);
            return true;
        }
        return false; // 无需回退
    }

    /**
     * 回退到风控审核中状态
     * 风控审核驳回 -> 回退到待风控审核
     * 合规补录驳回 -> 回退到待风控审核
     *
     * @return true-回退成功，false-无需回退
     */
    public boolean rollBackToRiskControlAuditing() {
        if (PayBizApplyDetailStatusEnum.RISK_CONTROL_REJECTED.getCode().equals(this.detailStatus) || 
            PayBizApplyDetailStatusEnum.COMPLIANCE_SUPPLEMENT_REJECTED.getCode().equals(this.detailStatus)) {
            // 风控审核驳回/合规补录驳回 -> 回退到待风控审核
            updateStatus(PayBizApplyStatusEnum.PROCESSING.getCode(),
                    PayBizApplyDetailStatusEnum.RISK_CONTROL_AUDITING.getCode(),
                    PayBizApplyDetailStatusEnum.RISK_CONTROL_AUDITING.getDesc());
            // 更新流程状态
            PayBizApplyFlowInfoBO flowInfo = getFlowInfoBO();
            flowInfo.markNodeAsInProgress(PayBizApplyDetailStatusEnum.COMPLIANCE_SUPPLEMENT_AUDITING.getCode());
            setFlowInfoBO(flowInfo);
            return true;
        }
        return false; // 无需回退
    }

    public void updateTaskId(Long taskId) {
        PayBizApplyExtraBO extraBO = PayBizApplyExtraBO.fromJsonString(this.extra);
        extraBO.setTaskId(taskId);
        this.extra = extraBO.toJsonString();
        touchMtime();
    }

    /**
     * 获取签约链接（待签约/签约失效状态时返回）
     *
     * @return 签约链接
     */
    public String getSignUrl() {
        if (PayBizApplyDetailStatusEnum.WAITING_LEGAL_SIGN.getCode().equals(detailStatus) || PayBizApplyDetailStatusEnum.LEGAL_SIGN_EXPIRED.getCode().equals(detailStatus)) {
            PayBizApplyExtraBO extraBO = PayBizApplyExtraBO.fromJsonString(this.extra);
            return extraBO.getLegalSignUrl();
        }
        if (PayBizApplyDetailStatusEnum.WAITING_SETTLEMENT_SIGN.getCode().equals(detailStatus) || PayBizApplyDetailStatusEnum.SETTLEMENT_SIGN_EXPIRED.getCode().equals(detailStatus)) {
            PayBizApplyExtraBO extraBO = PayBizApplyExtraBO.fromJsonString(this.extra);
            return extraBO.getSettlementSignUrl();
        }
        return null;
    }

    public void updateStatusToEnabling(String applyId) {
        this.status = PayBizApplyStatusEnum.PROCESSING.getCode();
        this.detailStatus = PayBizApplyDetailStatusEnum.ENABLING.getCode();
        this.detailStatusDesc = PayBizApplyDetailStatusEnum.ENABLING.getDesc();
        PayBizApplyExtraBO extraBO = PayBizApplyExtraBO.fromJsonString(this.extra);
        extraBO.setChangeAcquirerApplyId(applyId);
        this.extra = extraBO.toJsonString();
        this.priority = LocalDateTime.now();
        touchMtime();
    }

    public void updateStatusToEnabledFailed(String result) {
        this.status = PayBizApplyStatusEnum.PROCESSING.getCode();
        this.detailStatus = PayBizApplyDetailStatusEnum.ENABLE_FAILED.getCode();
        this.detailStatusDesc = PayBizApplyDetailStatusEnum.ENABLE_FAILED.getDesc();
        this.result = result;
        touchMtime();
    }

    public void updateStatusToContractFailed(String result) {
        this.status = PayBizApplyStatusEnum.FAILED.getCode();
        this.detailStatus = PayBizApplyDetailStatusEnum.CONTRACT_AUDIT_FAILED.getCode();
        this.detailStatusDesc = PayBizApplyDetailStatusEnum.CONTRACT_AUDIT_FAILED.getDesc();
        this.result = result;
        touchMtime();
    }

    public void updateStatusToExpired() {
        if (Objects.equals(detailStatus, PayBizApplyDetailStatusEnum.WAITING_LEGAL_SIGN.getCode())) {
            this.detailStatus = PayBizApplyDetailStatusEnum.LEGAL_SIGN_EXPIRED.getCode();
            this.detailStatusDesc = PayBizApplyDetailStatusEnum.LEGAL_SIGN_EXPIRED.getDesc();
        } else if (Objects.equals(detailStatus, PayBizApplyDetailStatusEnum.WAITING_SETTLEMENT_SIGN.getCode())) {
            this.detailStatus = PayBizApplyDetailStatusEnum.SETTLEMENT_SIGN_EXPIRED.getCode();
            this.detailStatusDesc = PayBizApplyDetailStatusEnum.SETTLEMENT_SIGN_EXPIRED.getDesc();
        }
        touchMtime();
    }

    public void updateToSignFailed() {
        this.status = PayBizApplyStatusEnum.FAILED.getCode();
        this.result = "签约失败";
        if (Objects.equals(detailStatus, PayBizApplyDetailStatusEnum.WAITING_LEGAL_SIGN.getCode())) {
            this.detailStatus = PayBizApplyDetailStatusEnum.LEGAL_SIGN_FAILED.getCode();
            this.detailStatusDesc = PayBizApplyDetailStatusEnum.LEGAL_SIGN_FAILED.getDesc();
        } else if (Objects.equals(detailStatus, PayBizApplyDetailStatusEnum.WAITING_SETTLEMENT_SIGN.getCode())) {
            this.detailStatus = PayBizApplyDetailStatusEnum.SETTLEMENT_SIGN_FAILED.getCode();
            this.detailStatusDesc = PayBizApplyDetailStatusEnum.SETTLEMENT_SIGN_FAILED.getDesc();
        }
        touchMtime();
    }

    public void updateToComplianceFailed(String result, String handledetail) {
        this.status = PayBizApplyStatusEnum.FAILED.getCode();
        this.detailStatus = PayBizApplyDetailStatusEnum.COMPLIANCE_SUPPLEMENT_REJECTED.getCode();
        this.detailStatusDesc = PayBizApplyDetailStatusEnum.COMPLIANCE_SUPPLEMENT_REJECTED.getDesc();
        this.result = result;
        PayBizApplyExtraBO extraBO = PayBizApplyExtraBO.fromJsonString(this.extra);
        extraBO.setHandleDetail(handledetail);
        this.extra = extraBO.toJsonString();
        touchMtime();
    }

    public void updateToWaitForSupplement() {
        this.detailStatus = PayBizApplyDetailStatusEnum.WAITING_SUPPLEMENT.getCode();
        this.detailStatusDesc = PayBizApplyDetailStatusEnum.WAITING_SUPPLEMENT.getDesc();
        touchMtime();
    }

    public void updateToRiskControlRejected(String failReason) {
        this.status = PayBizApplyStatusEnum.FAILED.getCode();
        this.detailStatus = PayBizApplyDetailStatusEnum.RISK_CONTROL_REJECTED.getCode();
        this.detailStatusDesc = PayBizApplyDetailStatusEnum.RISK_CONTROL_REJECTED.getDesc();
        this.result = failReason;
        touchMtime();
    }

    /**
     * 完成当前阶段并进入下一阶段 更新详细状态
     */
    public void completeCurrentStageAndMoveToNext() {
        // 标记当前节点为完成并启动下一个节点
        PayBizApplyFlowInfoBO flowInfo = getFlowInfoBO();
        boolean success = flowInfo.completeCurrentAndStartNext(LocalDateTime.now());
        if (success) {
            this.flowInfo = flowInfo.toJsonString();
        }
        Optional<PayBizApplyFlowNodeBO> currentNode = flowInfo.getCurrentNode();
        if (currentNode.isPresent()) {
            this.detailStatus = currentNode.get().getDetailStatus();
            this.detailStatusDesc = currentNode.get().getText();
        }
        this.priority = LocalDateTime.now();
        touchMtime();
    }

    public void markAsSuccess() {
        this.status = PayBizApplyStatusEnum.SUCCESS.getCode();
        this.detailStatus = PayBizApplyDetailStatusEnum.OPEN_SUCCESS.getCode();
        this.detailStatusDesc = PayBizApplyDetailStatusEnum.OPEN_SUCCESS.getDesc();
        // 将所有待处理/进行中的节点置为成功
        PayBizApplyFlowInfoBO flowInfo = getFlowInfoBO();
        flowInfo.markAllUnfinishedNodesAsSuccess(LocalDateTime.now());
        this.flowInfo = flowInfo.toJsonString();
        touchMtime();
    }

    public void updateSignUrl(String url) {
        PayBizApplyExtraBO extraBO = PayBizApplyExtraBO.fromJsonString(this.extra);
        if (PayBizApplyDetailStatusEnum.LEGAL_SIGN_EXPIRED.getCode().equals(detailStatus)) {
            extraBO.setLegalSignUrl(url);
            extraBO.setLegalSignUrlTime(System.currentTimeMillis());
            this.extra = extraBO.toJsonString();
            this.detailStatus = PayBizApplyDetailStatusEnum.WAITING_LEGAL_SIGN.getCode();
            this.priority = LocalDateTime.now();
            touchMtime();
        } else if (PayBizApplyDetailStatusEnum.SETTLEMENT_SIGN_EXPIRED.getCode().equals(detailStatus)) {
            extraBO.setSettlementSignUrl(url);
            extraBO.setSettlementSignUrlTime(System.currentTimeMillis());
            this.extra = extraBO.toJsonString();
            this.detailStatus = PayBizApplyDetailStatusEnum.WAITING_SETTLEMENT_SIGN.getCode();
            this.priority = LocalDateTime.now();
            touchMtime();
        }
    }

    public void saveLegalSignUrl(String url) {
        PayBizApplyExtraBO extraBO = PayBizApplyExtraBO.fromJsonString(this.extra);
        extraBO.setLegalSignUrl(url);
        extraBO.setLegalSignUrlTime(System.currentTimeMillis());
        this.extra = extraBO.toJsonString();
        this.priority = LocalDateTime.now();
        touchMtime();
    }

    public void saveSettlementSignUrl(String url) {
        PayBizApplyExtraBO extraBO = PayBizApplyExtraBO.fromJsonString(this.extra);
        extraBO.setSettlementSignUrl(url);
        extraBO.setSettlementSignUrlTime(System.currentTimeMillis());
        this.extra = extraBO.toJsonString();
        this.priority = LocalDateTime.now();
        touchMtime();
    }
}
