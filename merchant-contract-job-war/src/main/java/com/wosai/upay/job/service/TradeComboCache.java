package com.wosai.upay.job.service;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.wosai.trade.service.TradeComboService;
import com.wosai.trade.service.result.QueryTradeComboByIdResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

/**
 * TradeCombo缓存服务
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
public class TradeComboCache {

    @Autowired
    private TradeComboService tradeComboService;

    /**
     * 套餐信息缓存，缓存30分钟
     */
    private final Cache<Long, QueryTradeComboByIdResult> comboCache = CacheBuilder.newBuilder()
            .maximumSize(1000)  // 最大缓存1000个套餐
            .expireAfterWrite(30, TimeUnit.MINUTES)  // 写入后30分钟过期
            .build();

    /**
     * 获取套餐信息，优先从缓存获取
     * 
     * @param comboId 套餐ID
     * @return 套餐信息
     */
    public QueryTradeComboByIdResult getTradeComboById(Long comboId) {
        if (comboId == null) {
            return null;
        }

        try {
            return comboCache.get(comboId, () -> {
                log.debug("从远程服务获取套餐信息，comboId: {}", comboId);
                QueryTradeComboByIdResult result = tradeComboService.queryTradeComboById(comboId);
                if (result != null) {
                    log.debug("成功获取套餐信息，comboId: {}, name: {}", comboId, result.getName());
                } else {
                    log.warn("套餐信息不存在，comboId: {}", comboId);
                }
                return result;
            });
        } catch (Exception e) {
            log.error("获取套餐信息失败，comboId: {}", comboId, e);
            // 缓存异常时直接调用原始服务
            try {
                return tradeComboService.queryTradeComboById(comboId);
            } catch (Exception ex) {
                log.error("直接调用套餐服务也失败，comboId: {}", comboId, ex);
                return null;
            }
        }
    }

    /**
     * 获取套餐名称，优先从缓存获取
     * 
     * @param comboId 套餐ID
     * @return 套餐名称，如果获取失败返回默认名称
     */
    public String getTradeComboName(Long comboId) {
        if (comboId == null) {
            return "未知套餐";
        }

        QueryTradeComboByIdResult result = getTradeComboById(comboId);
        if (result != null && result.getName() != null) {
            return result.getName();
        }

        // 如果获取失败，返回默认名称
        return "套餐_" + comboId;
    }

    /**
     * 清除指定套餐的缓存
     * 
     * @param comboId 套餐ID
     */
    public void evictCombo(Long comboId) {
        if (comboId != null) {
            comboCache.invalidate(comboId);
            log.debug("清除套餐缓存，comboId: {}", comboId);
        }
    }

    /**
     * 清除所有缓存
     */
    public void evictAll() {
        comboCache.invalidateAll();
        log.info("清除所有套餐缓存");
    }

    /**
     * 获取缓存统计信息
     * 
     * @return 缓存统计信息字符串
     */
    public String getCacheStats() {
        return String.format("缓存统计 - 大小: %d, 命中率: %.2f%%, 请求次数: %d, 命中次数: %d, 未命中次数: %d",
                comboCache.size(),
                comboCache.stats().hitRate() * 100,
                comboCache.stats().requestCount(),
                comboCache.stats().hitCount(),
                comboCache.stats().missCount());
    }
}
