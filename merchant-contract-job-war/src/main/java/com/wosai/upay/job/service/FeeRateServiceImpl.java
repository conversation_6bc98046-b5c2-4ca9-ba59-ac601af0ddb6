package com.wosai.upay.job.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.PropertyNamingStrategy;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.fastjson.serializer.SerializeConfig;
import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.shouqianba.cua.enums.core.AcquirerOrgTypeEnum;
import com.wosai.common.utils.WosaiCollectionUtils;
import com.wosai.trade.service.result.ListMchFeeRateResult;
import com.wosai.upay.job.dto.IndirectChannelFeeRatePackageDTO;
import com.wosai.upay.job.refactor.dao.ContractStatusDAO;
import com.wosai.upay.job.refactor.dao.McAcquirerChangeDAO;
import com.wosai.upay.job.refactor.dao.McAcquirerDAO;
import com.wosai.upay.job.refactor.model.entity.ContractStatusDO;
import com.wosai.upay.job.refactor.model.entity.McAcquirerChangeDO;
import com.wosai.upay.job.refactor.model.entity.McAcquirerDO;
import com.wosai.upay.merchant.contract.exception.ContractBizException;
import com.wosai.upay.merchant.contract.service.FuyouService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.validation.constraints.NotBlank;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;


/**
 * 费率服务
 *
 * <AUTHOR>
 * @date 2024/10/11 09:28
 */
@Slf4j
@Service
@AutoJsonRpcServiceImpl
public class FeeRateServiceImpl implements com.wosai.upay.job.service.FeeRateService {

    @Autowired
    private FuyouService fuyouService;

    @Autowired
    private com.wosai.trade.service.FeeRateService tradeFeeRateService;

    @Autowired
    private ContractStatusDAO contractStatusDAO;

    @Autowired
    private McAcquirerDAO mcAcquirerDAO;

    @Autowired
    private McAcquirerChangeDAO mcAcquirerChangeDAO;

    private static final SerializeConfig config;

    static {
        config = new SerializeConfig();
        config.propertyNamingStrategy = PropertyNamingStrategy.SnakeCase;
    }

    /**
     * 根据富友模板获取费率信息
     *
     * @param templateCd   富友模板id
     * @return 枚举映射列表
     */
    @Override
    public String getFeeRateByTemplate(@NotBlank(message = "富友模板不能为空") String templateCd) {
        return fuyouService.getFeeRateByTemplate(templateCd);
    }

    /**
     * 获取间连通道的费率套餐信息
     *
     * @param merchantSn 商户号
     * @return 间连通道费率套餐信息列表
     */
    @Override
    public List<IndirectChannelFeeRatePackageDTO> getIndirectChannelFeeRatePackages(@NotBlank(message = "商户号不能为空") String merchantSn) {
        log.info("开始获取间连通道费率套餐信息，商户: {}", merchantSn);

        try {
            // 获取商户合同状态
            Optional<ContractStatusDO> contractStatusOpt = contractStatusDAO.getByMerchantSn(merchantSn);
            if (!contractStatusOpt.isPresent()) {
                log.warn("商户未进件成功，商户: {}", merchantSn);
                throw new ContractBizException("商户未进件成功");
            }

            ContractStatusDO contractStatusDO = contractStatusOpt.get();
            McAcquirerDO mcAcquirerDO = mcAcquirerDAO.getByAcquirer(contractStatusDO.getAcquirer());

            // 判断是否为间连通道（非三方收单机构）
            if (mcAcquirerDO.getType().equals(AcquirerOrgTypeEnum.THIRD_PARTY.getValue())) {
                log.info("商户为三方收单机构，无间连通道费率套餐，商户: {}", merchantSn);
                return new ArrayList<>();
            }

            // 获取商户有效费率
            List<ListMchFeeRateResult> mchFeeRateResults = tradeFeeRateService.listMchEffectFeeRates(merchantSn);

            if (WosaiCollectionUtils.isEmpty(mchFeeRateResults)) {
                log.warn("商户无有效费率，商户: {}", merchantSn);
                return new ArrayList<>();
            }

            // 转换为DTO
            List<IndirectChannelFeeRatePackageDTO> result = new ArrayList<>();
            for (ListMchFeeRateResult feeRateResult : mchFeeRateResults) {
                IndirectChannelFeeRatePackageDTO dto = convertToDTO(feeRateResult, merchantSn);
                result.add(dto);
            }

            log.info("获取间连通道费率套餐信息成功，商户: {}, 套餐数量: {}", merchantSn, result.size());
            return result;

        } catch (Exception e) {
            log.error("获取间连通道费率套餐信息失败，商户: {}", merchantSn, e);
            throw new ContractBizException("获取间连通道费率套餐信息失败: " + e.getMessage(), e);
        }
    }

    /**
     * 将ListMchFeeRateResult转换为IndirectChannelFeeRatePackageDTO
     */
    private IndirectChannelFeeRatePackageDTO convertToDTO(ListMchFeeRateResult feeRateResult, String merchantSn) {
        IndirectChannelFeeRatePackageDTO dto = new IndirectChannelFeeRatePackageDTO();

        // 设置套餐ID（使用费率ID或其他唯一标识）
        dto.setPackageId(generatePackageId(feeRateResult, merchantSn));

        // 设置套餐名称（根据payway生成名称）
        dto.setPackageName(generatePackageName(feeRateResult.getPayWay()));

        // 设置支付方式
        dto.setPayway(feeRateResult.getPayWay());

        // 设置费率信息
        IndirectChannelFeeRatePackageDTO.FeeRateInfo feeRateInfo = new IndirectChannelFeeRatePackageDTO.FeeRateInfo();

        // 设置基础费率
        feeRateInfo.setBasicFeeRate(feeRateResult.getBscFeeRate());

        // 设置渠道费率
        if (WosaiCollectionUtils.isNotEmpty(feeRateResult.getChannelFeeRates())) {
            List<ListMchFeeRateResult.ChannelFeeRate> channelFeeRates = feeRateResult.getChannelFeeRates();
            feeRateInfo.setChannelFeeRates(JSON.parseObject(JSON.toJSONString(channelFeeRates, config),
                    new TypeReference<List<Map<String, Object>>>() {}));
        }

        // 设置阶梯费率
        if (WosaiCollectionUtils.isNotEmpty(feeRateResult.getLadderFeeRates())) {
            List<ListMchFeeRateResult.LadderFeeRate> ladderFeeRates = feeRateResult.getLadderFeeRates();
            feeRateInfo.setLadderFeeRates(JSON.parseObject(JSON.toJSONString(ladderFeeRates, config),
                    new TypeReference<List<Map<String, Object>>>() {}));
        }

        // 设置渠道阶梯费率
        if (WosaiCollectionUtils.isNotEmpty(feeRateResult.getChannelLadderFeeRates())) {
            List<ListMchFeeRateResult.ChannelLadderFeeRate> channelLadderFeeRates = feeRateResult.getChannelLadderFeeRates();
            Map<String, List<Map<String, Object>>> feeRates = new HashMap<>();
            for (ListMchFeeRateResult.ChannelLadderFeeRate channelLadderFeeRate : channelLadderFeeRates) {
                feeRates.put(channelLadderFeeRate.getType(),
                        JSON.parseObject(JSON.toJSONString(channelLadderFeeRate.getLadderFeeRate(), config),
                                new TypeReference<List<Map<String, Object>>>() {}));
            }
            feeRateInfo.setChannelLadderFeeRates(feeRates);
        }

        dto.setFeeRateInfo(feeRateInfo);

        return dto;
    }

    /**
     * 生成套餐ID
     */
    private String generatePackageId(ListMchFeeRateResult feeRateResult, String merchantSn) {
        // 使用商户号和payway组合生成唯一ID
        return merchantSn + "_" + feeRateResult.getPayWay();
    }

    /**
     * 生成套餐名称
     */
    private String generatePackageName(Integer payway) {
        // 根据payway生成对应的套餐名称
        switch (payway) {
            case 1:
                return "支付宝间连通道费率套餐";
            case 2:
                return "微信间连通道费率套餐";
            case 3:
                return "银联间连通道费率套餐";
            default:
                return "间连通道费率套餐_" + payway;
        }
    }

}
