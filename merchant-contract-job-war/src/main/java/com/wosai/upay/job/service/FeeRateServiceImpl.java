package com.wosai.upay.job.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.PropertyNamingStrategy;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.fastjson.serializer.SerializeConfig;
import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.shouqianba.cua.enums.core.AcquirerOrgTypeEnum;
import com.wosai.common.utils.WosaiCollectionUtils;
import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.trade.service.TradeComboService;
import com.wosai.trade.service.bank.entity.FeeRateSnapshot;
import com.wosai.trade.service.result.FeeRateResponse;
import com.wosai.trade.service.result.ListMchFeeRateResult;
import com.wosai.trade.service.result.QueryTradeComboByIdResult;
import com.wosai.upay.job.dto.IndirectFeeRateComboDTO;
import com.wosai.upay.job.refactor.dao.ContractStatusDAO;
import com.wosai.upay.job.refactor.dao.McAcquirerChangeDAO;
import com.wosai.upay.job.refactor.dao.McAcquirerDAO;
import com.wosai.upay.job.refactor.model.entity.ContractStatusDO;
import com.wosai.upay.job.refactor.model.entity.McAcquirerChangeDO;
import com.wosai.upay.job.refactor.model.entity.McAcquirerDO;
import com.wosai.upay.job.util.CommonUtil;
import com.wosai.upay.merchant.contract.exception.ContractBizException;
import com.wosai.upay.merchant.contract.service.FuyouService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.validation.constraints.NotBlank;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static com.wosai.upay.job.biz.acquirer.AbstractAcquirerChangeBiz.INDIRECT_COMBO_SNAPSHOT;


/**
 * 费率服务
 *
 * <AUTHOR>
 * @date 2024/10/11 09:28
 */
@Slf4j
@Service
@AutoJsonRpcServiceImpl
public class FeeRateServiceImpl implements com.wosai.upay.job.service.FeeRateService {

    @Autowired
    private FuyouService fuyouService;

    @Autowired
    private com.wosai.trade.service.FeeRateService tradeFeeRateService;

    @Autowired
    private TradeComboService tradeComboService;

    @Autowired
    private ContractStatusDAO contractStatusDAO;

    @Autowired
    private McAcquirerDAO mcAcquirerDAO;

    @Autowired
    private McAcquirerChangeDAO mcAcquirerChangeDAO;

    private static final SerializeConfig config;

    static {
        config = new SerializeConfig();
        config.propertyNamingStrategy = PropertyNamingStrategy.SnakeCase;
    }

    /**
     * 根据富友模板获取费率信息
     *
     * @param templateCd 富友模板id
     * @return 枚举映射列表
     */
    @Override
    public String getFeeRateByTemplate(@NotBlank(message = "富友模板不能为空") String templateCd) {
        return fuyouService.getFeeRateByTemplate(templateCd);
    }

    /**
     * 获取间连通道的费率套餐信息
     *
     * @param merchantSn 商户号
     * @return 间连通道费率套餐信息列表
     */
    @Override
    public List<IndirectFeeRateComboDTO> getIndirectFeeRateCombo(@NotBlank(message = "商户号不能为空") String merchantSn) {
        log.info("开始获取间连通道费率套餐信息，商户: {}", merchantSn);

        try {
            // 获取商户合同状态
            Optional<ContractStatusDO> contractStatusOpt = contractStatusDAO.getByMerchantSn(merchantSn);
            if (!contractStatusOpt.isPresent()) {
                log.warn("商户未进件成功，商户: {}", merchantSn);
                throw new ContractBizException("商户未进件成功");
            }

            ContractStatusDO contractStatusDO = contractStatusOpt.get();
            McAcquirerDO mcAcquirerDO = mcAcquirerDAO.getByAcquirer(contractStatusDO.getAcquirer());


            // 获取费率信息 - 参考KeepAliveParamsBuilder.buildFeeRateInfo的逻辑
            List<IndirectFeeRateComboDTO> result = new ArrayList<>();

            // 判断是否为间连通道（非三方收单机构）
            if (mcAcquirerDO.getType().equals(AcquirerOrgTypeEnum.THIRD_PARTY.getValue())) {
                // 从当前有效费率获取套餐信息
                result = getFeeRatePackagesFromCurrent(merchantSn);
            } else {
                // 查询收单机构切换任务里面的快照信息
                Optional<McAcquirerChangeDO> mcAcquirerChangeDO = mcAcquirerChangeDAO.getLatestHasIndirectComboSnapshotSuccessApply(merchantSn);
                if (mcAcquirerChangeDO.isPresent()) {
                    // 从快照获取费率套餐信息
                    result = getFeeRateComboFromSnapshot(merchantSn, mcAcquirerChangeDO.get());
                }
            }

            log.info("获取间连通道费率套餐信息成功，商户: {}, 套餐数量: {}", merchantSn, result.size());
            return result;

        } catch (Exception e) {
            log.error("获取间连通道费率套餐信息失败，商户: {}", merchantSn, e);
            throw new ContractBizException("获取间连通道费率套餐信息失败: " + e.getMessage(), e);
        }
    }

    /**
     * 从快照获取费率套餐信息
     */
    private List<IndirectFeeRateComboDTO> getFeeRateComboFromSnapshot(String merchantSn, McAcquirerChangeDO mcAcquirerChangeDO) {
        log.info("从快照获取间连通道费率套餐信息，商户: {}", merchantSn);

        try {
            Map extra = CommonUtil.string2Map(mcAcquirerChangeDO.getExtra());
            List<FeeRateSnapshot> snapshotList = JSON.parseArray(JSON.toJSONString(WosaiMapUtils.getObject(extra, INDIRECT_COMBO_SNAPSHOT)), FeeRateSnapshot.class);

            if (WosaiCollectionUtils.isEmpty(snapshotList)) {
                log.warn("快照费率列表为空，商户: {}", merchantSn);
                return Collections.emptyList();
            }

            List<IndirectFeeRateComboDTO> result = new ArrayList<>();
            for (FeeRateSnapshot feeRateSnapshot : snapshotList) {
                // 从快照费率ID获取详细费率信息
                FeeRateResponse feeRateResponse = tradeFeeRateService.getMerchantFeeRateById(feeRateSnapshot.getFeeRateId());

                if (feeRateResponse != null) {
                    IndirectFeeRateComboDTO dto = convertFromFeeRateResponse(feeRateResponse, feeRateSnapshot.getPayWay());
                    result.add(dto);
                } else {
                    log.warn("费率ID: {} 获取详情失败，商户: {}, payway: {}",
                            feeRateSnapshot.getFeeRateId(), merchantSn, feeRateSnapshot.getPayWay());
                }
            }

            if (result.isEmpty()) {
                log.warn("快照费率转换后为空，商户: {}", merchantSn);
                return Collections.emptyList();
            }

            log.info("快照费率套餐获取成功，商户: {}, 套餐数量: {}", merchantSn, result.size());
            return result;

        } catch (Exception e) {
            log.error("快照费率套餐获取失败，回退到当前费率，商户: {}", merchantSn, e);
            return getFeeRatePackagesFromCurrent(merchantSn);
        }
    }

    /**
     * 从当前有效费率获取套餐信息
     */
    private List<IndirectFeeRateComboDTO> getFeeRatePackagesFromCurrent(String merchantSn) {
        log.info("从当前费率获取间连通道费率套餐信息，商户: {}", merchantSn);

        List<ListMchFeeRateResult> mchFeeRateResults = tradeFeeRateService.listMchEffectFeeRates(merchantSn);

        if (WosaiCollectionUtils.isEmpty(mchFeeRateResults)) {
            log.warn("商户无有效费率，商户: {}", merchantSn);
            return new ArrayList<>();
        }

        // 转换为DTO
        List<IndirectFeeRateComboDTO> result = new ArrayList<>();
        for (ListMchFeeRateResult feeRateResult : mchFeeRateResults) {
            IndirectFeeRateComboDTO dto = convertFromListMchFeeRateResult(feeRateResult);
            result.add(dto);
        }

        log.info("当前费率套餐获取成功，商户: {}, 套餐数量: {}", merchantSn, result.size());
        return result;
    }

    /**
     * 将FeeRateResponse转换为IndirectChannelFeeRatePackageDTO
     */
    private IndirectFeeRateComboDTO convertFromFeeRateResponse(FeeRateResponse feeRateResponse, Integer payway) {
        IndirectFeeRateComboDTO dto = new IndirectFeeRateComboDTO();

        // 设置套餐ID和名称
        dto.setComboId(String.valueOf(feeRateResponse.getTradeComboId()));
        QueryTradeComboByIdResult queryTradeComboByIdResult = tradeComboService.queryTradeComboById(feeRateResponse.getTradeComboId());
        dto.setComboName(queryTradeComboByIdResult.getName());
        // 设置支付方式
        dto.setPayway(payway);

        // 设置费率信息
        IndirectFeeRateComboDTO.FeeRateInfo feeRateInfo = new IndirectFeeRateComboDTO.FeeRateInfo();

        // 设置基础费率
        feeRateInfo.setBasicFeeRate(feeRateResponse.getFixedFeeRate());

        // 设置渠道费率
        if (WosaiCollectionUtils.isNotEmpty(feeRateResponse.getChannelFeeRates())) {
            List<FeeRateResponse.ChannelFeeRate> channelFeeRates = feeRateResponse.getChannelFeeRates();
            feeRateInfo.setChannelFeeRates(JSON.parseObject(JSON.toJSONString(channelFeeRates, config),
                    new TypeReference<List<Map<String, Object>>>() {
                    }));
        }

        // 设置阶梯费率
        if (WosaiCollectionUtils.isNotEmpty(feeRateResponse.getLadderFeeRates())) {
            List<FeeRateResponse.LadderFeeRate> ladderFeeRates = feeRateResponse.getLadderFeeRates();
            feeRateInfo.setLadderFeeRates(JSON.parseObject(JSON.toJSONString(ladderFeeRates, config),
                    new TypeReference<List<Map<String, Object>>>() {
                    }));
        }

        // 设置渠道阶梯费率
        if (WosaiCollectionUtils.isNotEmpty(feeRateResponse.getChannelLadderFeeRates())) {
            Map<String, List<Map<String, Object>>> feeRates = new HashMap<>();
            for (FeeRateResponse.ChannelLadderFeeRate channelLadderFeeRate : feeRateResponse.getChannelLadderFeeRates()) {
                feeRates.put(channelLadderFeeRate.getType(),
                        JSON.parseObject(JSON.toJSONString(channelLadderFeeRate.getLadderFeeRates(), config),
                                new TypeReference<List<Map<String, Object>>>() {
                                }));
            }
            feeRateInfo.setChannelLadderFeeRates(feeRates);
        }

        dto.setFeeRateInfo(feeRateInfo);

        return dto;
    }

    /**
     * 将ListMchFeeRateResult转换为IndirectChannelFeeRatePackageDTO
     */
    private IndirectFeeRateComboDTO convertFromListMchFeeRateResult(ListMchFeeRateResult feeRateResult) {
        IndirectFeeRateComboDTO dto = new IndirectFeeRateComboDTO();

        // 设置套餐ID（使用TradeComboId）
        dto.setComboId(feeRateResult.getTradeComboId() != null ? String.valueOf(feeRateResult.getTradeComboId()) : "");

        // 设置套餐名称
        String comboName = feeRateResult.getTradeAppName();
        dto.setComboName(comboName);

        // 设置支付方式
        dto.setPayway(feeRateResult.getPayWay());

        // 设置费率信息
        IndirectFeeRateComboDTO.FeeRateInfo feeRateInfo = new IndirectFeeRateComboDTO.FeeRateInfo();

        // 设置基础费率
        feeRateInfo.setBasicFeeRate(feeRateResult.getBscFeeRate());

        // 设置渠道费率
        if (WosaiCollectionUtils.isNotEmpty(feeRateResult.getChannelFeeRates())) {
            List<ListMchFeeRateResult.ChannelFeeRate> channelFeeRates = feeRateResult.getChannelFeeRates();
            feeRateInfo.setChannelFeeRates(JSON.parseObject(JSON.toJSONString(channelFeeRates, config),
                    new TypeReference<List<Map<String, Object>>>() {
                    }));
        }

        // 设置阶梯费率
        if (WosaiCollectionUtils.isNotEmpty(feeRateResult.getLadderFeeRates())) {
            List<ListMchFeeRateResult.LadderFeeRate> ladderFeeRates = feeRateResult.getLadderFeeRates();
            feeRateInfo.setLadderFeeRates(JSON.parseObject(JSON.toJSONString(ladderFeeRates, config),
                    new TypeReference<List<Map<String, Object>>>() {
                    }));
        }

        // 设置渠道阶梯费率
        if (WosaiCollectionUtils.isNotEmpty(feeRateResult.getChannelLadderFeeRates())) {
            List<ListMchFeeRateResult.ChannelLadderFeeRate> channelLadderFeeRates = feeRateResult.getChannelLadderFeeRates();
            Map<String, List<Map<String, Object>>> feeRates = new HashMap<>();
            for (ListMchFeeRateResult.ChannelLadderFeeRate channelLadderFeeRate : channelLadderFeeRates) {
                feeRates.put(channelLadderFeeRate.getType(),
                        JSON.parseObject(JSON.toJSONString(channelLadderFeeRate.getLadderFeeRate(), config),
                                new TypeReference<List<Map<String, Object>>>() {
                                }));
            }
            feeRateInfo.setChannelLadderFeeRates(feeRates);
        }

        dto.setFeeRateInfo(feeRateInfo);

        return dto;
    }

}
