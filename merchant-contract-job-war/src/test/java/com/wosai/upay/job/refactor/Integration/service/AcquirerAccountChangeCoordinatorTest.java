package com.wosai.upay.job.refactor.Integration.service;

import com.google.common.collect.Lists;
import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.job.BaseTest;
import com.wosai.upay.job.biz.acquirer.ChangeToLklBiz;
import com.wosai.upay.job.biz.acquirer.ChangeToLklV3Biz;
import com.wosai.upay.job.refactor.orchestrator.account.AcquirerAccountChangeCoordinator;
import com.wosai.upay.job.refactor.task.rotational.RotationalTask;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.annotation.Rollback;
import org.springframework.web.context.annotation.RequestScope;

import javax.annotation.Resource;

import java.util.Map;


/**
 * 收单机构测试
 *
 * <AUTHOR>
 */
@Slf4j
public class AcquirerAccountChangeCoordinatorTest extends BaseTest {

    @Resource
    private AcquirerAccountChangeCoordinator acquirerAccountChangeCoordinator;

    @Autowired
    private MerchantService merchantService;

    @Resource(name = "lklV3-AcquirerChangeBiz")
    private ChangeToLklV3Biz changeToLklV3Biz;

    @Test
    @Rollback(false)
    public void testBuildTask() {
        String merchantSn = "**************";
        String providerMerchantId = "42777871724357L";
        Long contractTaskId = 44291825797L;
        Long contractSubTaskId = 2826748L;
        Map<?, ?> newAccountInfo = merchantService.getMerchantBankAccountByMerchantId(merchantService.getMerchantBySn(merchantSn).get("id").toString());
        acquirerAccountChangeCoordinator.recordAcquirerAccountChangeAndCreatePollingTask(merchantSn, "lklV3", providerMerchantId, contractTaskId, contractSubTaskId, newAccountInfo);
    }

    @Resource
    private RotationalTask rotationalTask;


    @Test
    @Rollback(false)
    public void testProcessTask() {
        rotationalTask.batchHandleTasksByMainTaskIds(Lists.newArrayList(23766L));
    }

    @Test
    public void testUpdateTask() {
        String merchantSn = "**************";
        String merchantId = merchantService.getMerchantBySn(merchantSn).get("id").toString();
        Map account = merchantService.getMerchantBankAccountByMerchantId(merchantId);
        changeToLklV3Biz.checkTargetAcquirerChangeCardRule(account, "lklV3",merchantSn );
    }

}
