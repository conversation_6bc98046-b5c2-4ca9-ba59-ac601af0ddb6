package com.wosai.upay.job.refactor.unit.biz.rule;

import com.wosai.upay.job.refactor.biz.rule.AcquirerAccountChangeRuleEngine;
import com.wosai.upay.job.refactor.model.entity.AcquirerAccountChangeRecordDO;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Date;

import static org.junit.Assert.*;

/**
 * 规则引擎递归逻辑测试
 * 
 * 专门测试嵌套规则的递归处理能力
 *
 * <AUTHOR>
 * @since 2025-01-22
 */
@RunWith(MockitoJUnitRunner.class)
@Slf4j  
public class AcquirerAccountChangeRuleEngineRecursiveTest {

    @InjectMocks
    private AcquirerAccountChangeRuleEngine ruleEngine;

    private AcquirerAccountChangeRecordDO testRecord;
    
    private static final String BANK_ACCOUNT_CHANGE_TYPE_RULE = "{\n" +
            "  \"ruleName\": \"银行卡变更类型判断规则\",\n" +
            "  \"conditions\": [\n" +
            "    {\n" +
            "      \"logic\": \"OR\",\n" +
            "      \"conditions\": [\n" +
            "        {\n" +
            "          \"logic\": \"AND\",\n" +
            "          \"conditions\": [\n" +
            "            {\n" +
            "              \"fieldPath\": \"new_account_info.cardNumber\",\n" +
            "              \"operator\": \"NOT_EQUALS\",\n" +
            "              \"compareFieldPath\": \"old_account_info.cardNumber\"\n" +
            "            }\n" +
            "          ]\n" +
            "        },\n" +
            "        {\n" +
            "          \"logic\": \"AND\",\n" +
            "          \"conditions\": [\n" +
            "            {\n" +
            "              \"fieldPath\": \"new_account_info.type\",\n" +
            "              \"operator\": \"NOT_EQUALS\",\n" +
            "              \"compareFieldPath\": \"old_account_info.type\"\n" +
            "            }\n" +
            "          ]\n" +
            "        }\n" +
            "      ]\n" +
            "    }\n" +
            "  ]\n" +
            "}";
            

    @Before
    public void setUp() {
        // 创建测试记录
        testRecord = new AcquirerAccountChangeRecordDO();
        testRecord.setId(1L);
        testRecord.setMerchantSn("test_merchant_123");
        testRecord.setAcquirer("alipay");
        testRecord.setOldAccountInfo("{\"cardNumber\":\"****************\",\"type\":1,\"holder\":\"张三\",\"bankName\":\"招商银行\",\"openingBank\":\"招商银行上海分行\"}");
        testRecord.setNewAccountInfo("{\"cardNumber\":\"****************\",\"type\":2,\"holder\":\"李四\",\"bankName\":\"工商银行\",\"openingBank\":\"工商银行北京分行\"}");
        testRecord.setCtime(new Date());
        testRecord.setMtime(new Date());
        testRecord.setCompleteTime(new Date());
    }

    /**
     * 测试一层嵌套的OR逻辑
     */
    @Test
    public void testSimpleNestedOrLogic() {
        log.info("=== 测试一层嵌套OR逻辑 ===");
        
        // 规则：卡号变更 OR 银行名变更
        String ruleLogic = "{\n" +
                "  \"ruleName\": \"卡号或银行名变更\",\n" +
                "  \"conditions\": [\n" +
                "    {\n" +
                "      \"logic\": \"OR\",\n" +
                "      \"conditions\": [\n" +
                "        {\n" +
                "          \"fieldPath\": \"new_account_info.cardNumber\",\n" +
                "          \"operator\": \"NOT_EQUALS\",\n" +
                "          \"compareFieldPath\": \"old_account_info.cardNumber\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"fieldPath\": \"new_account_info.bankName\",\n" +
                "          \"operator\": \"NOT_EQUALS\",\n" +
                "          \"compareFieldPath\": \"old_account_info.bankName\"\n" +
                "        }\n" +
                "      ]\n" +
                "    }\n" +
                "  ]\n" +
            "}";

        boolean result = ruleEngine.evaluate(testRecord, ruleLogic);
        
        // 卡号从****************变更为****************，银行名从招商银行变更为工商银行
        // 卡号变更为true，银行名变更为true，OR逻辑结果应该为true
        assertTrue("一层嵌套OR逻辑：卡号或银行名变更应该匹配", result);
        log.info("一层嵌套OR逻辑测试通过：{}", result);
    }

    /**
     * 测试二层嵌套的复杂逻辑
     */
    @Test
    public void testDeepNestedLogic() {
        log.info("=== 测试二层嵌套复杂逻辑 ===");
        
        // 复杂规则：((卡号变更 OR 银行名变更) AND 账户类型变更) OR 持卡人变更
        String ruleLogic = "{\n" +
                "  \"ruleName\": \"复杂嵌套规则\",\n" +
                "  \"conditions\": [\n" +
                "    {\n" +
                "      \"logic\": \"OR\",\n" +
                "      \"conditions\": [\n" +
                "        {\n" +
                "          \"logic\": \"AND\",\n" +
                "          \"conditions\": [\n" +
                "            {\n" +
                "              \"logic\": \"OR\",\n" +
                "              \"conditions\": [\n" +
                "                {\n" +
                "                  \"fieldPath\": \"new_account_info.cardNumber\",\n" +
                "                  \"operator\": \"NOT_EQUALS\",\n" +
                "                  \"compareFieldPath\": \"old_account_info.cardNumber\"\n" +
                "                },\n" +
                "                {\n" +
                "                  \"fieldPath\": \"new_account_info.bankName\",\n" +
                "                  \"operator\": \"NOT_EQUALS\",\n" +
                "                  \"compareFieldPath\": \"old_account_info.bankName\"\n" +
                "                }\n" +
                "              ]\n" +
                "            },\n" +
                "            {\n" +
                "              \"fieldPath\": \"new_account_info.type\",\n" +
                "              \"operator\": \"NOT_EQUALS\",\n" +
                "              \"compareFieldPath\": \"old_account_info.type\"\n" +
                "            }\n" +
                "          ]\n" +
                "        },\n" +
                "        {\n" +
                "          \"fieldPath\": \"new_account_info.holder\",\n" +
                "          \"operator\": \"NOT_EQUALS\",\n" +
                "          \"compareFieldPath\": \"old_account_info.holder\"\n" +
                "        }\n" +
                "      ]\n" +
                "    }\n" +
                "  ]\n" +
            "}";

        boolean result = ruleEngine.evaluate(testRecord, ruleLogic);
        
        // 分析：
        // 1. 卡号变更：true (**************** -> ****************)
        // 2. 银行名变更：true (招商银行 -> 工商银行)
        // 3. 账户类型变更：true (1 -> 2)
        // 4. 持卡人变更：true (张三 -> 李四)
        // 
        // 逻辑计算：
        // ((卡号变更 OR 银行名变更) AND 账户类型变更) OR 持卡人变更
        // = ((true OR true) AND true) OR true
        // = (true AND true) OR true
        // = true OR true
        // = true
        
        assertTrue("二层嵌套复杂逻辑应该匹配", result);
        log.info("二层嵌套复杂逻辑测试通过：{}", result);
    }

    /**
     * 测试嵌套逻辑中的短路计算
     */
    @Test
    public void testNestedLogicShortCircuit() {
        log.info("=== 测试嵌套逻辑短路计算 ===");
        
        // 修改测试数据：只有持卡人变更，其他不变
        testRecord.setNewAccountInfo("{\"cardNumber\":\"****************\",\"type\":1,\"holder\":\"李四\",\"bankName\":\"招商银行\",\"openingBank\":\"招商银行上海分行\"}");
        
        // 规则：(卡号变更 AND 银行名变更) OR 持卡人变更
        String ruleLogic = "{\n" +
                "  \"ruleName\": \"短路测试规则\",\n" +
                "  \"conditions\": [\n" +
                "    {\n" +
                "      \"logic\": \"OR\",\n" +
                "      \"conditions\": [\n" +
                "        {\n" +
                "          \"logic\": \"AND\",\n" +
                "          \"conditions\": [\n" +
                "            {\n" +
                "              \"fieldPath\": \"new_account_info.cardNumber\",\n" +
                "              \"operator\": \"NOT_EQUALS\",\n" +
                "              \"compareFieldPath\": \"old_account_info.cardNumber\"\n" +
                "            },\n" +
                "            {\n" +
                "              \"fieldPath\": \"new_account_info.bankName\",\n" +
                "              \"operator\": \"NOT_EQUALS\",\n" +
                "              \"compareFieldPath\": \"old_account_info.bankName\"\n" +
                "            }\n" +
                "          ]\n" +
                "        },\n" +
                "        {\n" +
                "          \"fieldPath\": \"new_account_info.holder\",\n" +
                "          \"operator\": \"NOT_EQUALS\",\n" +
                "          \"compareFieldPath\": \"old_account_info.holder\"\n" +
                "        }\n" +
                "      ]\n" +
                "    }\n" +
                "  ]\n" +
            "}";

        boolean result = ruleEngine.evaluate(testRecord, ruleLogic);
        
        // 分析：
        // 1. 卡号变更：false (相同)
        // 2. 银行名变更：false (相同)
        // 3. 持卡人变更：true (张三 -> 李四)
        // 
        // 逻辑计算：
        // (卡号变更 AND 银行名变更) OR 持卡人变更
        // = (false AND false) OR true
        // = false OR true
        // = true（OR逻辑短路：第二个条件为true，直接返回true）
        
        assertTrue("嵌套逻辑短路计算应该正确", result);
        log.info("嵌套逻辑短路计算测试通过：{}", result);
    }

    /**
     * 测试三层嵌套的极端情况
     */
    @Test
    public void testThreeLevelNesting() {
        log.info("=== 测试三层嵌套极端情况 ===");
        
        // 三层嵌套：(((卡号变更 OR 银行名变更) AND 账户类型不变) OR 持卡人变更) AND 开户行变更
        String ruleLogic = "{\n" +
                "  \"ruleName\": \"三层嵌套规则\",\n" +
                "  \"conditions\": [\n" +
                "    {\n" +
                "      \"logic\": \"AND\",\n" +
                "      \"conditions\": [\n" +
                "        {\n" +
                "          \"logic\": \"OR\",\n" +
                "          \"conditions\": [\n" +
                "            {\n" +
                "              \"logic\": \"AND\",\n" +
                "              \"conditions\": [\n" +
                "                {\n" +
                "                  \"logic\": \"OR\",\n" +
                "                  \"conditions\": [\n" +
                "                    {\n" +
                "                      \"fieldPath\": \"new_account_info.cardNumber\",\n" +
                "                      \"operator\": \"NOT_EQUALS\",\n" +
                "                      \"compareFieldPath\": \"old_account_info.cardNumber\"\n" +
                "                    },\n" +
                "                    {\n" +
                "                      \"fieldPath\": \"new_account_info.bankName\",\n" +
                "                      \"operator\": \"NOT_EQUALS\",\n" +
                "                      \"compareFieldPath\": \"old_account_info.bankName\"\n" +
                "                    }\n" +
                "                  ]\n" +
                "                },\n" +
                "                {\n" +
                "                  \"fieldPath\": \"new_account_info.type\",\n" +
                "                  \"operator\": \"EQUALS\",\n" +
                "                  \"compareFieldPath\": \"old_account_info.type\"\n" +
                "                }\n" +
                "              ]\n" +
                "            },\n" +
                "            {\n" +
                "              \"fieldPath\": \"new_account_info.holder\",\n" +
                "              \"operator\": \"NOT_EQUALS\",\n" +
                "              \"compareFieldPath\": \"old_account_info.holder\"\n" +
                "            }\n" +
                "          ]\n" +
                "        },\n" +
                "        {\n" +
                "          \"fieldPath\": \"new_account_info.openingBank\",\n" +
                "          \"operator\": \"NOT_EQUALS\",\n" +
                "          \"compareFieldPath\": \"old_account_info.openingBank\"\n" +
                "        }\n" +
                "      ]\n" +
                "    }\n" +
                "  ]\n" +
            "}";

        boolean result = ruleEngine.evaluate(testRecord, ruleLogic);
        
        // 分析：
        // 1. 最内层OR：卡号变更(true) OR 银行名变更(true) = true
        // 2. 第二层AND：上面结果(true) AND 账户类型不变(false，因为1->2) = false
        // 3. 第三层OR：上面结果(false) OR 持卡人变更(true) = true  
        // 4. 最外层AND：上面结果(true) AND 开户行变更(true，招商银行上海分行->工商银行北京分行) = true
        
        assertTrue("三层嵌套逻辑应该正确处理", result);
        log.info("三层嵌套逻辑测试通过：{}", result);
    }

    /**
     * 测试空值和非空值判断的嵌套逻辑
     */
    @Test
    public void testNestedNullCheckLogic() {
        log.info("=== 测试嵌套空值检查逻辑 ===");
        
        // 规则：(新卡号不为空 AND 旧卡号不为空) AND (新卡号 != 旧卡号)
        String ruleLogic = "{\n" +
                "  \"ruleName\": \"嵌套空值检查\",\n" +
                "  \"conditions\": [\n" +
                "    {\n" +
                "      \"logic\": \"AND\",\n" +
                "      \"conditions\": [\n" +
                "        {\n" +
                "          \"logic\": \"AND\",\n" +
                "          \"conditions\": [\n" +
                "            {\n" +
                "              \"fieldPath\": \"new_account_info.cardNumber\",\n" +
                "              \"operator\": \"IS_NOT_NULL\"\n" +
                "            },\n" +
                "            {\n" +
                "              \"fieldPath\": \"old_account_info.cardNumber\",\n" +
                "              \"operator\": \"IS_NOT_NULL\"\n" +
                "            }\n" +
                "          ]\n" +
                "        },\n" +
                "        {\n" +
                "          \"fieldPath\": \"new_account_info.cardNumber\",\n" +
                "          \"operator\": \"NOT_EQUALS\",\n" +
                "          \"compareFieldPath\": \"old_account_info.cardNumber\"\n" +
                "        }\n" +
                "      ]\n" +
                "    }\n" +
                "  ]\n" +
            "}";

        boolean result = ruleEngine.evaluate(testRecord, ruleLogic);
        
        // 分析：
        // 1. 新卡号不为空：true
        // 2. 旧卡号不为空：true  
        // 3. 内层AND：true AND true = true
        // 4. 卡号变更：true (**************** != ****************)
        // 5. 外层AND：true AND true = true
        
        assertTrue("嵌套空值检查逻辑应该正确", result);
        log.info("嵌套空值检查逻辑测试通过：{}", result);
    }

    /**
     * 测试递归逻辑的错误恢复能力
     */
    @Test
    public void testRecursiveErrorRecovery() {
        log.info("=== 测试递归逻辑错误恢复 ===");
        
        // 包含无效字段的嵌套规则
        String invalidRuleLogic = "{\n" +
                "  \"ruleName\": \"包含无效字段的规则\",\n" +
                "  \"conditions\": [\n" +
                "    {\n" +
                "      \"logic\": \"OR\",\n" +
                "      \"conditions\": [\n" +
                "        {\n" +
                "          \"fieldPath\": \"invalid_field.nonexistent\",\n" +
                "          \"operator\": \"NOT_EQUALS\",\n" +
                "          \"compareValue\": \"test\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"fieldPath\": \"new_account_info.cardNumber\",\n" +
                "          \"operator\": \"NOT_EQUALS\",\n" +
                "          \"compareFieldPath\": \"old_account_info.cardNumber\"\n" +
                "        }\n" +
                "      ]\n" +
                "    }\n" +
                "  ]\n" +
            "}";

        boolean result = ruleEngine.evaluate(testRecord, invalidRuleLogic);
        
        // 即使有无效字段，第二个条件仍然有效，OR逻辑应该能正确处理
        assertTrue("递归逻辑应该能处理部分无效条件", result);
        log.info("递归逻辑错误恢复测试通过：{}", result);
    }

    /**
     * 测试银行卡变更类型判断规则 - 同名换卡场景
     */
    @Test
    public void testBankAccountChangeTypeRule_SameNameCardChange() {
        log.info("=== 测试银行卡变更类型判断规则 - 同名换卡场景 ===");
        
        // 设置测试数据：对私账户，仅卡号变更，证件号码不变
        testRecord.setOldAccountInfo("{\"cardNumber\":\"6217000010001234567\",\"type\":1,\"holder\":\"张三\",\"holderCertificateNumber\":\"310101199001011234\"}");
        testRecord.setNewAccountInfo("{\"cardNumber\":\"6217000010007654321\",\"type\":1,\"holder\":\"张三\",\"holderCertificateNumber\":\"310101199001011234\"}");


        boolean result = ruleEngine.evaluate(testRecord, BANK_ACCOUNT_CHANGE_TYPE_RULE);
        
        // 分析：对私账户，卡号变更，证件号码不变，类型不变 - 应该匹配同名换卡规则
        assertTrue("对私账户同名换卡应该匹配规则", result);
        log.info("对私账户同名换卡测试通过：{}", result);
    }

    /**
     * 测试银行卡变更类型判断规则 - 异名换卡场景（对私）
     */
    @Test
    public void testBankAccountChangeTypeRule_DifferentNameCardChange_Private() {
        log.info("=== 测试银行卡变更类型判断规则 - 异名换卡场景（对私） ===");
        
        // 设置测试数据：对私账户，证件号码变更
        testRecord.setOldAccountInfo("{\"cardNumber\":\"6217000010001234567\",\"type\":1,\"holder\":\"张三\",\"holderCertificateNumber\":\"310101199001011234\"}");
        testRecord.setNewAccountInfo("{\"cardNumber\":\"6217000010007654321\",\"type\":1,\"holder\":\"张三\",\"holderCertificateNumber\":\"310101199001015678\"}");

        boolean result = ruleEngine.evaluate(testRecord, BANK_ACCOUNT_CHANGE_TYPE_RULE);
        
        // 分析：对私账户，证件号码变更，卡号变更 - 应该匹配异名换卡规则
        assertTrue("对私账户异名换卡应该匹配规则", result
        );
        log.info("对私账户异名换卡测试通过：{}", result);
    }

    /**
     * 测试银行卡变更类型判断规则 - 异名换卡场景（对公）
     */
    @Test
    public void testBankAccountChangeTypeRule_DifferentNameCardChange_Public() {
        log.info("=== 测试银行卡变更类型判断规则 - 异名换卡场景（对公） ===");
        
        // 设置测试数据：对公账户，账户名变更
        testRecord.setOldAccountInfo("{\"cardNumber\":\"6217000010001234567\",\"type\":2,\"holder\":\"张三公司\",\"holderCertificateNumber\":\"91310000123456789X\"}");
        testRecord.setNewAccountInfo("{\"cardNumber\":\"6217000010007654321\",\"type\":2,\"holder\":\"李四公司\",\"holderCertificateNumber\":\"91310000123456789X\"}");

        boolean result = ruleEngine.evaluate(testRecord, BANK_ACCOUNT_CHANGE_TYPE_RULE);
        
        // 分析：对公账户，账户名变更，卡号变更 - 应该匹配异名换卡规则
        assertTrue("对公账户异名换卡应该匹配规则", result);
        log.info("对公账户异名换卡测试通过：{}", result);
    }

    /**
     * 测试银行卡变更类型判断规则 - 类型变更场景
     */
    @Test
    public void testBankAccountChangeTypeRule_TypeChange() {
        log.info("=== 测试银行卡变更类型判断规则 - 类型变更场景 ===");
        
        // 设置测试数据：账户类型从对私变更为对公
        testRecord.setOldAccountInfo("{\"cardNumber\":\"6217000010001234567\",\"type\":1,\"holder\":\"张三\",\"holderCertificateNumber\":\"310101199001011234\"}");
        testRecord.setNewAccountInfo("{\"cardNumber\":\"6217000010007654321\",\"type\":2,\"holder\":\"张三公司\",\"holderCertificateNumber\":\"91310000123456789X\"}");

        boolean result = ruleEngine.evaluate(testRecord, BANK_ACCOUNT_CHANGE_TYPE_RULE);
        
        // 分析：账户类型从1变为2 - 应该匹配类型变更规则
        assertTrue("账户类型变更应该匹配规则", result);
        log.info("账户类型变更测试通过：{}", result);
    }
}