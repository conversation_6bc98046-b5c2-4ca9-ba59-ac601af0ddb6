package com.wosai.upay.job.service;

import com.shouqianba.cua.enums.core.AcquirerOrgTypeEnum;
import com.wosai.trade.service.result.ListMchFeeRateResult;
import com.wosai.upay.job.dto.IndirectChannelFeeRatePackageDTO;
import com.wosai.upay.job.refactor.dao.ContractStatusDAO;
import com.wosai.upay.job.refactor.dao.McAcquirerDAO;
import com.wosai.upay.job.refactor.model.entity.ContractStatusDO;
import com.wosai.upay.job.refactor.model.entity.McAcquirerDO;
import com.wosai.upay.merchant.contract.exception.ContractBizException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

/**
 * FeeRateServiceImpl测试类
 */
@ExtendWith(MockitoExtension.class)
class FeeRateServiceImplTest {

    @Mock
    private com.wosai.trade.service.FeeRateService tradeFeeRateService;

    @Mock
    private ContractStatusDAO contractStatusDAO;

    @Mock
    private McAcquirerDAO mcAcquirerDAO;

    @InjectMocks
    private FeeRateServiceImpl feeRateService;

    private String testMerchantSn = "TEST_MERCHANT_001";

    @BeforeEach
    void setUp() {
        // 初始化测试数据
    }

    @Test
    void testGetIndirectChannelFeeRatePackages_Success() {
        // 准备测试数据
        ContractStatusDO contractStatus = new ContractStatusDO();
        contractStatus.setAcquirer("TEST_ACQUIRER");

        McAcquirerDO mcAcquirer = new McAcquirerDO();
        mcAcquirer.setType(AcquirerOrgTypeEnum.DIRECT.getValue()); // 非三方

        ListMchFeeRateResult feeRateResult = new ListMchFeeRateResult();
        feeRateResult.setPayWay(1); // 支付宝
        feeRateResult.setBscFeeRate("0.6%");

        List<ListMchFeeRateResult> feeRateResults = new ArrayList<>();
        feeRateResults.add(feeRateResult);

        // Mock行为
        when(contractStatusDAO.getByMerchantSn(testMerchantSn))
                .thenReturn(Optional.of(contractStatus));
        when(mcAcquirerDAO.getByAcquirer("TEST_ACQUIRER"))
                .thenReturn(mcAcquirer);
        when(tradeFeeRateService.listMchEffectFeeRates(testMerchantSn))
                .thenReturn(feeRateResults);

        // 执行测试
        List<IndirectChannelFeeRatePackageDTO> result = 
                feeRateService.getIndirectChannelFeeRatePackages(testMerchantSn);

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.size());
        
        IndirectChannelFeeRatePackageDTO dto = result.get(0);
        assertEquals(testMerchantSn + "_1", dto.getPackageId());
        assertEquals("支付宝间连通道费率套餐", dto.getPackageName());
        assertEquals(Integer.valueOf(1), dto.getPayway());
        assertNotNull(dto.getFeeRateInfo());
        assertEquals("0.6%", dto.getFeeRateInfo().getBasicFeeRate());
    }

    @Test
    void testGetIndirectChannelFeeRatePackages_ThirdPartyAcquirer() {
        // 准备测试数据
        ContractStatusDO contractStatus = new ContractStatusDO();
        contractStatus.setAcquirer("TEST_ACQUIRER");

        McAcquirerDO mcAcquirer = new McAcquirerDO();
        mcAcquirer.setType(AcquirerOrgTypeEnum.THIRD_PARTY.getValue()); // 三方

        // Mock行为
        when(contractStatusDAO.getByMerchantSn(testMerchantSn))
                .thenReturn(Optional.of(contractStatus));
        when(mcAcquirerDAO.getByAcquirer("TEST_ACQUIRER"))
                .thenReturn(mcAcquirer);

        // 执行测试
        List<IndirectChannelFeeRatePackageDTO> result = 
                feeRateService.getIndirectChannelFeeRatePackages(testMerchantSn);

        // 验证结果 - 三方收单机构应该返回空列表
        assertNotNull(result);
        assertEquals(0, result.size());
    }

    @Test
    void testGetIndirectChannelFeeRatePackages_MerchantNotFound() {
        // Mock行为 - 商户未找到
        when(contractStatusDAO.getByMerchantSn(testMerchantSn))
                .thenReturn(Optional.empty());

        // 执行测试并验证异常
        ContractBizException exception = assertThrows(ContractBizException.class, () -> {
            feeRateService.getIndirectChannelFeeRatePackages(testMerchantSn);
        });

        assertEquals("商户未进件成功", exception.getMessage());
    }

    @Test
    void testGeneratePackageName() {
        // 通过反射测试私有方法或者通过公共接口间接测试
        // 这里我们通过集成测试来验证套餐名称的生成
        ContractStatusDO contractStatus = new ContractStatusDO();
        contractStatus.setAcquirer("TEST_ACQUIRER");

        McAcquirerDO mcAcquirer = new McAcquirerDO();
        mcAcquirer.setType(AcquirerOrgTypeEnum.DIRECT.getValue());

        // 测试不同的payway
        ListMchFeeRateResult feeRateResult1 = new ListMchFeeRateResult();
        feeRateResult1.setPayWay(1);
        feeRateResult1.setBscFeeRate("0.6%");

        ListMchFeeRateResult feeRateResult2 = new ListMchFeeRateResult();
        feeRateResult2.setPayWay(2);
        feeRateResult2.setBscFeeRate("0.6%");

        List<ListMchFeeRateResult> feeRateResults = new ArrayList<>();
        feeRateResults.add(feeRateResult1);
        feeRateResults.add(feeRateResult2);

        when(contractStatusDAO.getByMerchantSn(testMerchantSn))
                .thenReturn(Optional.of(contractStatus));
        when(mcAcquirerDAO.getByAcquirer("TEST_ACQUIRER"))
                .thenReturn(mcAcquirer);
        when(tradeFeeRateService.listMchEffectFeeRates(testMerchantSn))
                .thenReturn(feeRateResults);

        List<IndirectChannelFeeRatePackageDTO> result = 
                feeRateService.getIndirectChannelFeeRatePackages(testMerchantSn);

        assertEquals(2, result.size());
        assertEquals("支付宝间连通道费率套餐", result.get(0).getPackageName());
        assertEquals("微信间连通道费率套餐", result.get(1).getPackageName());
    }
}
