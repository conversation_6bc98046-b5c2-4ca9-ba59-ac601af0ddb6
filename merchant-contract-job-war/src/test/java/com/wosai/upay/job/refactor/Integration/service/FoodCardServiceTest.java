package com.wosai.upay.job.refactor.Integration.service;

import avro.shaded.com.google.common.collect.Maps;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.shouqianba.cua.enums.businesslog.LogPlatformEnum;
import com.shouqianba.cua.enums.contract.AcquirerTypeEnum;
import com.shouqianba.cua.enums.contract.ProviderEnum;
import com.shouqianba.cua.model.http.LogParamsDto;
import com.wosai.upay.common.bean.PageInfo;
import com.wosai.upay.core.service.TradeConfigService;
import com.wosai.upay.job.BaseTest;
import com.wosai.upay.job.adapter.apollo.ApplicationApolloConfig;
import com.wosai.upay.job.biz.BusinessLogBiz;
import com.wosai.upay.job.biz.SubBizParamsBiz;
import com.wosai.upay.job.model.dto.MerchantProviderParamsCustomDto;
import com.wosai.upay.job.model.dto.request.ChangeAcquirerReqDTO;
import com.wosai.upay.job.model.dto.request.FoodCardConfigTradeParamReqDTO;
import com.wosai.upay.job.model.dto.response.AcquirerChangeTaskRspDTO;
import com.wosai.upay.job.model.dto.response.CuaCommonResultDTO;
import com.wosai.upay.job.model.subBizParams.SubBizConfig;
import com.wosai.upay.job.refactor.biz.params.MerchantTradeParamsBiz;
import com.wosai.upay.job.refactor.model.entity.MerchantProviderParamsDO;
import com.wosai.upay.job.refactor.model.enums.PaywayEnum;
import com.wosai.upay.job.service.AcquirerService;
import com.wosai.upay.job.service.FoodCardService;
import com.wosai.upay.job.service.MerchantProviderParamsService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;

import javax.annotation.Resource;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * 饭卡支付测试
 *
 * <AUTHOR>
 */
@Slf4j
public class FoodCardServiceTest extends BaseTest {

    @Resource
    private FoodCardService foodCardService;

    @Resource
    private SubBizParamsBiz subBizParamsBiz;

    @Autowired
    private ApplicationApolloConfig applicationApolloConfig;

    @Autowired
    private TradeConfigService tradeConfigService;

    @Resource
    private MerchantProviderParamsService merchantProviderParamsService;

    @MockBean
    private MerchantTradeParamsBiz merchantTradeParamsBiz;

    @Test
    public void tempTest() {
        // merchantProviderParamsService.findMerchantSubBizParams("21690004060485");
        PageInfo pageInfo = new PageInfo();
        pageInfo.setPage(1);
        pageInfo.setPageSize(100);
        MerchantProviderParamsCustomDto merchantProviderParamsCustomDto = new MerchantProviderParamsCustomDto();
        merchantProviderParamsCustomDto.setMerchant_sn("21690004060485");
        merchantProviderParamsService.findMerchantProviderParamsList(pageInfo, merchantProviderParamsCustomDto);
    }

    @Test
    public void testConfigFoodCardTradeParam() {
        FoodCardConfigTradeParamReqDTO foodCardConfigTradeParamReqDTO = new FoodCardConfigTradeParamReqDTO();
        foodCardConfigTradeParamReqDTO.setMerchantSn("21690004060717");
        foodCardConfigTradeParamReqDTO.setPartnerId("PAI202505120000001");
        foodCardService.configFoodCardTradeParam(foodCardConfigTradeParamReqDTO);
    }


    @Test
    public void testFeeCombo() {
        subBizParamsBiz.doComboByPayway("21690004028632", getSubBizConfig("4"), 31);
    }

    private SubBizConfig getSubBizConfig(String tradeAppId) {
        //设置费率套餐
        Map appIdSubBizMap = applicationApolloConfig.getAppIdSubBiz();
        List<Map> values = JSONArray.parseArray(JSONObject.toJSONString(appIdSubBizMap.values()), Map.class);
        Map<String, Map> tradeNameConfig = values.stream().collect(Collectors.toMap(sub -> MapUtils.getString(sub, "mappingTradeAppId"), sub -> sub, (k1, k2) -> k1));
        Map map = tradeNameConfig.get(tradeAppId);
        if (MapUtils.isEmpty(map)) {
            log.info("doComboAfterChangeParam empty,appName:{}", tradeAppId);
            return null;
        }
        return JSONObject.parseObject(JSON.toJSONString(map), SubBizConfig.class);
    }

    @Before
    public void setUp() throws Exception {
        List<MerchantProviderParamsDO> merchantProviderParamsDOS = new ArrayList<>();
        MerchantProviderParamsDO merchantProviderParamsDO = new MerchantProviderParamsDO();
        merchantProviderParamsDO.setId("**********");
        merchantProviderParamsDO.setProviderMerchantId("**********");
        merchantProviderParamsDO.setParentMerchantId("**********");
        merchantProviderParamsDO.setRuleGroupId("**********");
        merchantProviderParamsDO.setProvider(ProviderEnum.PROVIDER_LEXIN.getValue());
        merchantProviderParamsDO.setPayway(PaywayEnum.ACQUIRER.getValue());
        merchantProviderParamsDOS.add(merchantProviderParamsDO);
        Mockito.when(merchantTradeParamsBiz.listParamsByMerchantSn(Mockito.anyString())).thenReturn(merchantProviderParamsDOS);
    }

    @Test
    public void configLianlianFoodCardTradeParamTest() {
        FoodCardConfigTradeParamReqDTO tradeParamReqDTO = new FoodCardConfigTradeParamReqDTO();
        tradeParamReqDTO.setProviderAgreementNo("2025081400765028");
        tradeParamReqDTO.setProviderMchId("91a1cd85-82c0-49b3-921a-49dba3e01298");
        tradeParamReqDTO.setProviderMchSubId("91a1cd85-82c0-49b3-921a-49dba3e01298");
        tradeParamReqDTO.setMerchantSn("21690003361258");
        tradeParamReqDTO.setPartnerId("PAI202505120000001");
        String content ="{\n" +
                "            \"merchantSn\": \"21690003361258\",\n" +
                "            \"partnerId\": \"PAI202505120000001\",\n" +
                "            \"providerMchId\": \"91a1cd85-82c0-49b3-921a-49dba3e01298\",\n" +
                "            \"providerMchSubId\": \"91a1cd85-82c0-49b3-921a-49dba3e01298\",\n" +
                "            \"providerAgreementNo\": \"2025081400765028\"\n" +
                "        }";
        FoodCardConfigTradeParamReqDTO foodCardConfigTradeParamReqDTO = JSONObject.parseObject(content, FoodCardConfigTradeParamReqDTO.class);
        CuaCommonResultDTO cuaCommonResultDTO = foodCardService.configLianlianFoodCardTradeParam(foodCardConfigTradeParamReqDTO);
        assertThat(cuaCommonResultDTO.isSuccess()).isTrue();
    }
}
