package com.wosai.upay.job.refactor.unit.task.rotational;

import com.alibaba.fastjson.JSON;
import com.shouqianba.cua.enums.contract.ContractSubTaskProcessStatusEnum;
import com.wosai.upay.job.dto.BankAccountSimpleInfoBO;
import com.wosai.upay.job.refactor.dao.ContractSubTaskDAO;
import com.wosai.upay.job.refactor.model.bo.InternalScheduleSubTaskProcessResultBO;
import com.wosai.upay.job.refactor.model.entity.ContractSubTaskDO;
import com.wosai.upay.job.refactor.model.entity.InternalScheduleMainTaskDO;
import com.wosai.upay.job.refactor.model.entity.InternalScheduleSubTaskDO;
import com.wosai.upay.job.refactor.model.enums.InternalScheduleSubTaskStatusEnum;
import com.wosai.upay.job.refactor.task.rotational.AcquirerAccountChangePollingSubTaskProcessor;
import com.wosai.upay.job.refactor.task.rotational.RotationalTask;
import com.wosai.upay.job.refactor.task.rotational.builder.AcquirerAccountChangePollingTaskBuilder;
import com.wosai.upay.job.refactor.task.rotational.entity.RotationalSubTaskTypeEnum;
import com.wosai.upay.job.refactor.task.rotational.entity.RotationalTaskContext;
import com.wosai.upay.job.service.AcquirerAccountChangeValidationService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Optional;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

/**
 * 收单机构换卡轮询任务处理器单元测试
 * 
 * <AUTHOR>
 * @since 2025-01-22
 */
@Slf4j
@RunWith(MockitoJUnitRunner.class)
public class AcquirerAccountChangePollingSubTaskProcessorTest {

    @Mock
    private ContractSubTaskDAO contractSubTaskDAO;

    @Mock
    private AcquirerAccountChangeValidationService accountChangeValidationService;

    @Mock
    private RotationalTask rotationalTask;

    @InjectMocks
    private AcquirerAccountChangePollingSubTaskProcessor processor;

    private String merchantSn;
    private Long contractSubTaskId;
    private InternalScheduleMainTaskDO mainTaskDO;
    private InternalScheduleSubTaskDO subTaskDO;

    @Before
    public void setUp() {
        merchantSn = "TEST_MERCHANT_001";
        contractSubTaskId = 12345L;

        mainTaskDO = new InternalScheduleMainTaskDO();
        mainTaskDO.setId(1L);
        mainTaskDO.setMerchantSn(merchantSn);

        subTaskDO = new InternalScheduleSubTaskDO();
        subTaskDO.setId(1L);
    }

    /**
     * 测试新增轮询任务
     */
    @Test
    public void testBuildTaskForContractTask() {
        log.info("=== 测试新增收单机构换卡轮询任务 ===");

        // Given: 构建轮询任务上下文
        RotationalTaskContext rotationalTaskContext = buildRotationalTaskContext();

        // When: 执行任务构建
        rotationalTask.buildTaskForContractTask(rotationalTaskContext);

        // Then: 验证调用
        verify(rotationalTask, times(1)).buildTaskForContractTask(eq(rotationalTaskContext));

        log.info("新增轮询任务构建完成, merchantSn={}, contractSubTaskId={}",
                merchantSn, contractSubTaskId);
    }

    /**
     * 测试新增轮询任务 - 上下文为空
     */
    @Test
    public void testBuildTaskForContractTask_NullContext() {
        log.info("=== 测试新增轮询任务，上下文为空的情况 ===");

        // Given: 空的上下文
        RotationalTaskContext nullContext = null;

        // Mock 异常抛出
        doThrow(new RuntimeException("轮询任务上下文为空"))
                .when(rotationalTask).buildTaskForContractTask(nullContext);

        // When & Then: 验证异常抛出
        try {
            rotationalTask.buildTaskForContractTask(nullContext);
            fail("应该抛出异常");
        } catch (Exception e) {
            assertEquals("轮询任务上下文为空", e.getMessage());
        }

        verify(rotationalTask, times(1)).buildTaskForContractTask(nullContext);
    }

    /**
     * 测试新增轮询任务 - 完整参数
     */
    @Test
    public void testBuildTaskForContractTask_WithFullParams() {
        log.info("=== 测试新增轮询任务，包含完整参数 ===");

        // Given: 构建包含完整参数的上下文
        BankAccountSimpleInfoBO oldAccount = new BankAccountSimpleInfoBO();
        oldAccount.setCardNumber("6217000010001234567");
        oldAccount.setHolder("李四");
        oldAccount.setType(1);

        BankAccountSimpleInfoBO newAccount = new BankAccountSimpleInfoBO();
        newAccount.setCardNumber("6217000010007654321");
        newAccount.setHolder("李四");
        newAccount.setType(2);

        RotationalTaskContext taskContext = RotationalTaskContext.builder()
                .merchantSn(merchantSn)
                .contractTaskId(1L)
                .contractSubTaskId(contractSubTaskId)
                .rotationId("ACCP_" + System.currentTimeMillis())
                .subTaskTypeEnum(RotationalSubTaskTypeEnum.ACQUIRER_ACCOUNT_CHANGE_POLLING)
                .belongToContractTask(true)
                .addParam(AcquirerAccountChangePollingSubTaskProcessor.CONTRACT_SUB_TASK_ID_KEY, contractSubTaskId)
                .addParam(AcquirerAccountChangePollingSubTaskProcessor.ACQUIRER_KEY, "lklV3")
                .addParam(AcquirerAccountChangePollingSubTaskProcessor.ACQUIRER_MERCHANT_ID_KEY, "LKL_MERCHANT_001")
                .addParam(AcquirerAccountChangePollingSubTaskProcessor.OLD_ACCOUNT_INFO_KEY, JSON.toJSONString(oldAccount))
                .addParam(AcquirerAccountChangePollingSubTaskProcessor.NEW_ACCOUNT_INFO_KEY, JSON.toJSONString(newAccount))
                .addParam(AcquirerAccountChangePollingSubTaskProcessor.REMARK_KEY, "换卡测试")
                .build();

        // When: 执行任务构建
        rotationalTask.buildTaskForContractTask(taskContext);

        // Then: 验证调用
        verify(rotationalTask, times(1)).buildTaskForContractTask(eq(taskContext));

        log.info("包含完整参数的轮询任务构建完成, merchantSn={}, acquirer=lklV3", merchantSn);
    }

    /**
     * 构建RotationalTaskContext（用于新增任务测试）
     */
    private RotationalTaskContext buildRotationalTaskContext() {
        return RotationalTaskContext.builder()
                .merchantSn(merchantSn)
                .contractTaskId(1L)
                .contractSubTaskId(contractSubTaskId)
                .rotationId("ACCP_" + System.currentTimeMillis())
                .subTaskTypeEnum(RotationalSubTaskTypeEnum.ACQUIRER_ACCOUNT_CHANGE_POLLING)
                .belongToContractTask(true)
                .addParam(AcquirerAccountChangePollingSubTaskProcessor.CONTRACT_SUB_TASK_ID_KEY, contractSubTaskId)
                .addParam(AcquirerAccountChangePollingSubTaskProcessor.ACQUIRER_KEY, "lklV3")
                .addParam(AcquirerAccountChangePollingSubTaskProcessor.ACQUIRER_MERCHANT_ID_KEY, "LKL_MERCHANT_001")
                .addParam(AcquirerAccountChangePollingSubTaskProcessor.REMARK_KEY, "测试换卡")
                .build();
    }


    /**
     * 测试获取子任务类型
     */
    @Test
    public void testGetSubTaskType() {
        RotationalSubTaskTypeEnum taskType = processor.getSubTaskType();
        assertEquals(RotationalSubTaskTypeEnum.ACQUIRER_ACCOUNT_CHANGE_POLLING, taskType);
    }

    /**
     * 测试参数不完整的情况
     */
    @Test
    public void testHandleRotationalSubTask_InvalidParams() {
        log.info("=== 测试参数不完整的情况 ===");

        // Given: 缺少必要参数的任务上下文
        String incompleteContext = AcquirerAccountChangePollingTaskBuilder.create()
                .merchantSn(merchantSn)
                .belongToContractTask(true)
                // 缺少 contractSubTaskId
                .acquirer("lklV3")
                .build();

        mainTaskDO.setContext(incompleteContext);

        // When: 执行任务处理
        InternalScheduleSubTaskProcessResultBO result = processor.handleRotationalSubTask(mainTaskDO, subTaskDO);

        // Then: 应该返回失败结果
        assertEquals(InternalScheduleSubTaskStatusEnum.PROCESS_FAIL, result.getStatus());
        assertTrue("错误信息应该包含参数不完整", result.getResult().contains("任务参数不完整"));

        log.info("✅ 参数不完整测试通过");
    }

    /**
     * 测试contract_sub_task不存在的情况
     */
    @Test
    public void testHandleRotationalSubTask_ContractSubTaskNotFound() {
        log.info("=== 测试contract_sub_task不存在的情况 ===");

        // Given: 完整的任务上下文，但contract_sub_task不存在
        String taskContext = buildValidTaskContext();
        mainTaskDO.setContext(taskContext);

        when(contractSubTaskDAO.getByPrimaryKey(contractSubTaskId)).thenReturn(Optional.empty());

        // When: 执行任务处理
        InternalScheduleSubTaskProcessResultBO result = processor.handleRotationalSubTask(mainTaskDO, subTaskDO);

        // Then: 应该返回失败结果
        assertEquals(InternalScheduleSubTaskStatusEnum.PROCESS_FAIL, result.getStatus());
        assertTrue("错误信息应该包含找不到记录", result.getResult().contains("找不到对应的contract_sub_task记录"));

        verify(contractSubTaskDAO).getByPrimaryKey(contractSubTaskId);

        log.info("✅ contract_sub_task不存在测试通过");
    }

    /**
     * 测试contract_sub_task成功状态的处理
     */
    @Test
    public void testHandleRotationalSubTask_Success() {
        log.info("=== 测试contract_sub_task成功状态的处理 ===");

        // Given: 完整的任务上下文和成功的contract_sub_task
        String taskContext = buildValidTaskContext();
        mainTaskDO.setContext(taskContext);

        ContractSubTaskDO contractSubTask = createContractSubTask(ContractSubTaskProcessStatusEnum.PROCESS_SUCCESS, "换卡成功");
        when(contractSubTaskDAO.getByPrimaryKey(contractSubTaskId)).thenReturn(Optional.of(contractSubTask));

        Long recordId = 67890L;
        when(accountChangeValidationService.recordAccountChange(any())).thenReturn(recordId);

        // When: 执行任务处理
        InternalScheduleSubTaskProcessResultBO result = processor.handleRotationalSubTask(mainTaskDO, subTaskDO);

        // Then: 应该返回成功结果
        assertEquals(InternalScheduleSubTaskStatusEnum.PROCESS_SUCCESS, result.getStatus());
        assertTrue("结果信息应该包含recordId", result.getResult().contains("recordId=" + recordId));
        assertEquals("响应消息应该是SUCCESS", "SUCCESS", result.getResponseMsg());

        verify(contractSubTaskDAO).getByPrimaryKey(contractSubTaskId);
        verify(accountChangeValidationService).recordAccountChange(any());

        log.info("✅ 成功状态处理测试通过");
    }

    /**
     * 测试contract_sub_task失败状态的处理
     */
    @Test
    public void testHandleRotationalSubTask_Failed() {
        log.info("=== 测试contract_sub_task失败状态的处理 ===");

        // Given: 完整的任务上下文和失败的contract_sub_task
        String taskContext = buildValidTaskContext();
        mainTaskDO.setContext(taskContext);

        String failureReason = "收单机构拒绝换卡申请";
        ContractSubTaskDO contractSubTask = createContractSubTask(ContractSubTaskProcessStatusEnum.PROCESS_FAIL, failureReason);
        when(contractSubTaskDAO.getByPrimaryKey(contractSubTaskId)).thenReturn(Optional.of(contractSubTask));

        // When: 执行任务处理
        InternalScheduleSubTaskProcessResultBO result = processor.handleRotationalSubTask(mainTaskDO, subTaskDO);

        // Then: 应该返回失败结果
        assertEquals(InternalScheduleSubTaskStatusEnum.PROCESS_FAIL, result.getStatus());
        assertTrue("结果信息应该包含失败原因", result.getResult().contains(failureReason));
        assertEquals("响应消息应该是失败原因", failureReason, result.getResponseMsg());

        verify(contractSubTaskDAO).getByPrimaryKey(contractSubTaskId);
        verifyNoInteractions(accountChangeValidationService);

        log.info("✅ 失败状态处理测试通过");
    }

    /**
     * 测试contract_sub_task处理中状态的处理
     */
    @Test
    public void testHandleRotationalSubTask_Processing() {
        log.info("=== 测试contract_sub_task处理中状态的处理 ===");

        // Given: 完整的任务上下文和处理中的contract_sub_task
        String taskContext = buildValidTaskContext();
        mainTaskDO.setContext(taskContext);

        ContractSubTaskDO contractSubTask = createContractSubTask(ContractSubTaskProcessStatusEnum.WAIT_PROCESS, "处理中");
        when(contractSubTaskDAO.getByPrimaryKey(contractSubTaskId)).thenReturn(Optional.of(contractSubTask));

        // When: 执行任务处理
        InternalScheduleSubTaskProcessResultBO result = processor.handleRotationalSubTask(mainTaskDO, subTaskDO);

        // Then: 应该返回等待结果
        assertEquals(InternalScheduleSubTaskStatusEnum.WAIT_EXTERNAL_RESULT, result.getStatus());
        assertTrue("结果信息应该表明等待处理", result.getResult().contains("等待contract_sub_task处理完成"));

        verify(contractSubTaskDAO).getByPrimaryKey(contractSubTaskId);
        verifyNoInteractions(accountChangeValidationService);

        log.info("✅ 处理中状态处理测试通过");
    }

    /**
     * 测试记录账户变更失败的情况
     */
    @Test
    public void testHandleRotationalSubTask_RecordFailed() {
        log.info("=== 测试记录账户变更失败的情况 ===");

        // Given: 完整的任务上下文和成功的contract_sub_task，但记录失败
        String taskContext = buildValidTaskContext();
        mainTaskDO.setContext(taskContext);

        ContractSubTaskDO contractSubTask = createContractSubTask(ContractSubTaskProcessStatusEnum.PROCESS_SUCCESS, "换卡成功");
        when(contractSubTaskDAO.getByPrimaryKey(contractSubTaskId)).thenReturn(Optional.of(contractSubTask));

        when(accountChangeValidationService.recordAccountChange(any()))
                .thenThrow(new RuntimeException("数据库连接失败"));

        // When: 执行任务处理
        InternalScheduleSubTaskProcessResultBO result = processor.handleRotationalSubTask(mainTaskDO, subTaskDO);

        // Then: 应该返回失败结果
        assertEquals(InternalScheduleSubTaskStatusEnum.PROCESS_FAIL, result.getStatus());
        assertTrue("结果信息应该包含记录失败", result.getResult().contains("换卡成功但记录失败"));

        verify(accountChangeValidationService).recordAccountChange(any());

        log.info("✅ 记录失败处理测试通过");
    }

    /**
     * 构建有效的任务上下文
     */
    private String buildValidTaskContext() {
        BankAccountSimpleInfoBO oldAccount = new BankAccountSimpleInfoBO();
        oldAccount.setCardNumber("6217000010001234567");

        BankAccountSimpleInfoBO newAccount = new BankAccountSimpleInfoBO();
        newAccount.setCardNumber("6217000010007654321");

        return AcquirerAccountChangePollingTaskBuilder.create()
                .merchantSn(merchantSn)
                .contractTaskId(1L)
                .contractSubTaskId(contractSubTaskId)
                .belongToContractTask(true)
                .acquirer("lklV3")
                .acquirerMerchantId("LKL_MERCHANT_001")
                .oldAccountInfo(oldAccount)
                .newAccountInfo(newAccount)
                .remark("测试换卡")
                .build();
    }

    /**
     * 创建contract_sub_task实体
     */
    private ContractSubTaskDO createContractSubTask(ContractSubTaskProcessStatusEnum status, String processResult) {
        ContractSubTaskDO contractSubTask = new ContractSubTaskDO();
        contractSubTask.setId(contractSubTaskId);
        contractSubTask.setStatus(status.getValue());
        contractSubTask.setResult(processResult);
        return contractSubTask;
    }


}
