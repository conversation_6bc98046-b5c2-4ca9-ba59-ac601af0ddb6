package com.wosai.upay.job.refactor.unit.service;

import com.wosai.upay.job.adapter.apollo.ApplicationApolloConfig;
import com.wosai.upay.job.dto.AccountChangeValidationResult;
import com.wosai.upay.job.dto.BankAccountSimpleInfoBO;
import com.wosai.upay.job.refactor.dao.AcquirerAccountChangeRecordDAO;
import com.wosai.upay.job.refactor.dao.AcquirerAccountChangeRuleDAO;
import com.wosai.upay.job.refactor.model.entity.AcquirerAccountChangeRuleDO;
import com.wosai.upay.job.refactor.model.enums.AcquirerAccountChangeStatusEnum;
import com.wosai.upay.job.refactor.service.impl.AcquirerAccountChangeValidationServiceImpl;
import com.wosai.upay.job.refactor.biz.rule.AcquirerAccountChangeRuleEngine;
import com.wosai.upay.job.refactor.biz.params.MerchantTradeParamsBiz;
import com.wosai.upay.job.refactor.model.entity.AcquirerAccountChangeRecordDO;
import com.shouqianba.cua.enums.contract.AcquirerTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.Optional;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 收单机构账户变更校验服务单元测试
 *
 * <AUTHOR>
 * @since 2025-01-22
 */
@Slf4j
@RunWith(MockitoJUnitRunner.class)
public class AcquirerAccountChangeValidationServiceTest {

    @Mock
    private AcquirerAccountChangeRuleDAO accountChangeRuleDAO;

    @Mock
    private AcquirerAccountChangeRecordDAO accountChangeRecordDAO;

    @Mock
    private AcquirerAccountChangeRuleEngine ruleEngine;

    @Mock
    private MerchantTradeParamsBiz merchantTradeParamsBiz;

    @Mock
    private ApplicationApolloConfig applicationApolloConfig;

    @InjectMocks
    private AcquirerAccountChangeValidationServiceImpl validationService;

    private String merchantSn;
    private String acquirer;
    private BankAccountSimpleInfoBO oldAccountInfo;
    private BankAccountSimpleInfoBO newAccountInfo;

    @Before
    public void setUp() {
        merchantSn = "TEST_MERCHANT_001";
        acquirer = "lklV3";
        // providerMerchantId 提供默认值，走"机构商户号维度"统计
        when(merchantTradeParamsBiz.getMerchantAcquirerMerchantId(eq(merchantSn), any(AcquirerTypeEnum.class)))
                .thenReturn(java.util.Optional.of("ACQ_MCH_123"));
        // 不配置兜底逻辑，避免不必要的 stubbing（优先按枚举获取）

        // Mock ApplicationApolloConfig
        when(applicationApolloConfig.getAcquirerChangeCardCheckSwitch()).thenReturn(true);
        // Mock白名单 - 默认不在白名单中
        when(applicationApolloConfig.getAcquirerChangeCardWhitelist())
                .thenReturn(java.util.Arrays.asList());

        // 构建旧账户信息
        oldAccountInfo = new BankAccountSimpleInfoBO();
        oldAccountInfo.setCardNumber("6217000010001234567");
        oldAccountInfo.setType(2); // 对私
        oldAccountInfo.setHolder("张三");

        // 构建新账户信息
        newAccountInfo = new BankAccountSimpleInfoBO();
        newAccountInfo.setCardNumber("6217000010007654321");
        newAccountInfo.setType(2); // 对私
        newAccountInfo.setHolder("张三");
    }

    /**
     * 测试场景1：无规则配置
     * 预期：直接允许变更，hasRuleRestriction=false
     */
    @Test
    public void testValidateAccountChange_NoRule() {
        log.info("=== 测试场景1：无规则配置 ===");

        // Given: 无规则配置
        when(accountChangeRuleDAO.getByAcquirer(acquirer)).thenReturn(Optional.empty());

        // When: 执行校验
        AccountChangeValidationResult result = validationService.validateAccountChange(
                merchantSn, acquirer, oldAccountInfo, newAccountInfo);

        // Then: 验证结果
        assertTrue("应该允许变更", result.isAllowed());
        assertFalse("应该标识无规则限制", result.isHasRuleRestriction());
        assertEquals("消息应该正确", "无换卡限制规则，可以进行变更", result.getMessage());
        assertEquals("使用次数应该为0", 0, result.getUsedCount());
        assertEquals("最大次数应该为0", 0, result.getMaxCount());
        assertEquals("周期天数应该为0", 0, result.getPeriodDays());
        assertNotNull("下次变更时间应该有值(立即可变更)", result.getNextChangeTime());

        // 验证交互
        verify(accountChangeRuleDAO, times(1)).getByAcquirer(acquirer);
        verifyNoMoreInteractions(accountChangeRuleDAO, accountChangeRecordDAO, ruleEngine);

        log.info("✅ 测试场景1通过");
    }

    /**
     * 测试场景2：零限制规则匹配，拦截变更
     * 预期：拦截变更，hasRuleRestriction=true
     */
    @Test
    public void testValidateAccountChange_ZeroLimitRule_Blocked() {
        log.info("=== 测试场景2：零限制规则匹配，拦截变更 ===");

        // Given: 零限制规则，且规则匹配
        AcquirerAccountChangeRuleDO rule = createZeroLimitRule("跨行换卡禁止");
        when(accountChangeRuleDAO.getByAcquirer(acquirer)).thenReturn(Optional.of(rule));
        when(ruleEngine.evaluate(any(), eq(rule.getRuleLogic()))).thenReturn(true);

        // When: 执行校验
        AccountChangeValidationResult result = validationService.validateAccountChange(
                merchantSn, acquirer, oldAccountInfo, newAccountInfo);

        // Then: 验证结果
        assertFalse("应该拦截变更", result.isAllowed());
        assertTrue("ruleMatched 应为 true", result.isRuleMatched());
        assertTrue("应该标识有规则限制", result.isHasRuleRestriction());
        assertTrue("消息应该包含规则名称", result.getMessage().contains("跨行换卡禁止"));
        assertTrue("消息应该表明被禁止", result.getMessage().contains("不被允许"));
        assertEquals("使用次数应该为0", 0, result.getUsedCount());
        assertEquals("最大次数应该为0", 0, result.getMaxCount());
        assertEquals("周期天数应该为30", 30, result.getPeriodDays());
        assertNull("下次变更时间应该为空", result.getNextChangeTime());

        log.info("✅ 测试场景2通过");
    }

    /**
     * 测试场景3：零限制规则不匹配，允许变更
     * 预期：允许变更，hasRuleRestriction=true
     */
    @Test
    public void testValidateAccountChange_ZeroLimitRule_Allowed() {
        log.info("=== 测试场景3：零限制规则不匹配，允许变更 ===");

        // Given: 零限制规则，但规则不匹配
        AcquirerAccountChangeRuleDO rule = createZeroLimitRule("特定卡号禁止");
        when(accountChangeRuleDAO.getByAcquirer(acquirer)).thenReturn(Optional.of(rule));
        when(ruleEngine.evaluate(any(), eq(rule.getRuleLogic()))).thenReturn(false);

        // When: 执行校验
        AccountChangeValidationResult result = validationService.validateAccountChange(
                merchantSn, acquirer, oldAccountInfo, newAccountInfo);

        // Then: 验证结果
        assertTrue("应该允许变更", result.isAllowed());
        assertFalse("ruleMatched 应为 false", result.isRuleMatched());
        assertTrue("应该标识有规则限制", result.isHasRuleRestriction());
        assertTrue("消息应该包含规则名称", result.getMessage().contains("特定卡号禁止"));
        assertTrue("消息应该表明不匹配规则", result.getMessage().contains("不匹配限制规则"));

        log.info("✅ 测试场景3通过");
    }

    /**
     * 测试场景4：次数限制规则不匹配，允许变更
     * 预期：允许变更，不消耗次数，hasRuleRestriction=true
     */
    @Test
    public void testValidateAccountChange_CountLimitRule_NotMatched() {
        log.info("=== 测试场景4：次数限制规则不匹配，允许变更 ===");

        // Given: 次数限制规则，但规则不匹配
        AcquirerAccountChangeRuleDO rule = createCountLimitRule("银行名变更限制", 3, 30);
        when(accountChangeRuleDAO.getByAcquirer(acquirer)).thenReturn(Optional.of(rule));
        when(ruleEngine.evaluate(any(), eq(rule.getRuleLogic()))).thenReturn(false);

        // When: 执行校验
        AccountChangeValidationResult result = validationService.validateAccountChange(
                merchantSn, acquirer, oldAccountInfo, newAccountInfo);

        // Then: 验证结果
        assertTrue("应该允许变更", result.isAllowed());
        assertFalse("ruleMatched 应为 false", result.isRuleMatched());
        assertTrue("应该标识有规则限制", result.isHasRuleRestriction());
        assertTrue("消息应该表明不匹配规则", result.getMessage().contains("不匹配限制规则"));
        assertEquals("使用次数应该为0", 0, result.getUsedCount());
        assertEquals("最大次数应该为3", 3, result.getMaxCount());
        assertEquals("周期天数应该为30", 30, result.getPeriodDays());

        // 验证不会查询历史记录
        verify(accountChangeRecordDAO, never()).findSuccessRecords(any(), any(), any(), any());

        log.info("✅ 测试场景4通过");
    }

    /**
     * 测试场景5：次数限制规则匹配，但未超限，允许变更
     * 预期：允许变更，显示剩余次数，hasRuleRestriction=true
     */
    @Test
    public void testValidateAccountChange_CountLimitRule_AllowedWithinLimit() {
        log.info("=== 测试场景5：次数限制规则匹配，但未超限，允许变更 ===");

        // Given: 次数限制规则，规则匹配，当前使用1次（未超限）
        AcquirerAccountChangeRuleDO rule = createCountLimitRule("卡号变更限制", 3, 30);
        when(accountChangeRuleDAO.getByAcquirer(acquirer)).thenReturn(Optional.of(rule));
        when(ruleEngine.evaluate(any(), eq(rule.getRuleLogic()))).thenReturn(true);

        // Mock countAccountChanges方法的调用链 - 返回空列表表示没有历史记录
        when(accountChangeRecordDAO.findSuccessRecords(any(), any(), any(), any()))
                .thenReturn(java.util.Collections.emptyList());
        // 仅 stub 实际会用到的 5 参方法，避免不必要 stubbing
        when(accountChangeRecordDAO.findSuccessRecords(anyString(), anyString(), anyString(), any(Date.class), any(Date.class)))
                .thenReturn(java.util.Collections.emptyList());

        // When: 执行校验
        AccountChangeValidationResult result = validationService.validateAccountChange(
                merchantSn, acquirer, oldAccountInfo, newAccountInfo);

        // Then: 验证结果
        assertTrue("应该允许变更", result.isAllowed());
        assertTrue("ruleMatched 应为 true", result.isRuleMatched());
        assertTrue("应该标识有规则限制", result.isHasRuleRestriction());
        assertTrue("消息应该包含剩余次数", result.getMessage().contains("还可变更"));
        assertTrue("消息应该包含规则名称", result.getMessage().contains("卡号变更限制"));
        assertEquals("使用次数应该为0", 0, result.getUsedCount());
        assertEquals("最大次数应该为3", 3, result.getMaxCount());
        assertEquals("周期天数应该为30", 30, result.getPeriodDays());
        assertNotNull("下次变更时间应该有值(立即可变更)", result.getNextChangeTime());

        log.info("✅ 测试场景5通过");
    }

    /**
     * 测试场景6：次数限制规则匹配，超限，拦截变更
     * 预期：拦截变更，提供下次变更时间，hasRuleRestriction=true
     */
    @Test
    public void testValidateAccountChange_CountLimitRule_Blocked() {
        log.info("=== 测试场景6：次数限制规则匹配，超限，拦截变更 ===");

        // Given: 次数限制规则，规则匹配，当前使用3次（已超限）
        AcquirerAccountChangeRuleDO rule = createCountLimitRule("频繁换卡限制", 2, 7);
        when(accountChangeRuleDAO.getByAcquirer(acquirer)).thenReturn(Optional.of(rule));
        when(ruleEngine.evaluate(any(), eq(rule.getRuleLogic()))).thenReturn(true);

        // 模拟已达到最大次数 - 创建完整的历史记录
        com.wosai.upay.job.refactor.model.entity.AcquirerAccountChangeRecordDO record1 = createAccountChangeRecord(
                1L, merchantSn, acquirer,
                "{\"cardNumber\":\"6217000010001111111\",\"bankName\":\"招商银行\"}",
                "{\"cardNumber\":\"6217000010002222222\",\"bankName\":\"工商银行\"}",
                5); // 5天前

        com.wosai.upay.job.refactor.model.entity.AcquirerAccountChangeRecordDO record2 = createAccountChangeRecord(
                2L, merchantSn, acquirer,
                "{\"cardNumber\":\"6217000010002222222\",\"bankName\":\"工商银行\"}",
                "{\"cardNumber\":\"6217000010003333333\",\"bankName\":\"中国银行\"}",
                2); // 2天前

        when(accountChangeRecordDAO.findSuccessRecords(any(), any(), any(), any()))
                .thenReturn(java.util.Arrays.asList(record1, record2));
        when(accountChangeRecordDAO.findSuccessRecords(anyString(), anyString(), anyString(), any(Date.class), any(Date.class)))
                .thenReturn(java.util.Arrays.asList(record1, record2));

        // Mock ruleEngine.evaluate：第一次调用（validateCountLimitRule中的规则匹配）返回true
        // 后续调用（shouldCountRecord中的记录过滤）也返回true
        when(ruleEngine.evaluate(any(), eq(rule.getRuleLogic()))).thenReturn(true);

        // When: 执行校验
        AccountChangeValidationResult result = validationService.validateAccountChange(
                merchantSn, acquirer, oldAccountInfo, newAccountInfo);

        // Then: 验证结果
        assertFalse("应该拦截变更", result.isAllowed());
        assertTrue("ruleMatched 应为 true", result.isRuleMatched());
        assertTrue("应该标识有规则限制", result.isHasRuleRestriction());
        assertTrue("消息应该包含超限提示", result.getMessage().contains("超过最大变更次数限制"));
        assertTrue("消息应该包含规则名称", result.getMessage().contains("频繁换卡限制"));
        assertEquals("使用次数应该为2", 2, result.getUsedCount());
        assertEquals("最大次数应该为2", 2, result.getMaxCount());
        assertEquals("周期天数应该为7", 7, result.getPeriodDays());
        assertNotNull("下次变更时间不应该为空", result.getNextChangeTime());

        log.info("✅ 测试场景6通过");
    }

    /**
     * 测试场景7：规则名称为空的处理
     * 预期：使用默认名称"未命名规则"
     */
    @Test
    public void testValidateAccountChange_EmptyRuleName() {
        log.info("=== 测试场景7：规则名称为空的处理 ===");

        // Given: 规则名称为空
        AcquirerAccountChangeRuleDO rule = createZeroLimitRule(null);
        when(accountChangeRuleDAO.getByAcquirer(acquirer)).thenReturn(Optional.of(rule));
        when(ruleEngine.evaluate(any(), eq(rule.getRuleLogic()))).thenReturn(true);

        // When: 执行校验
        AccountChangeValidationResult result = validationService.validateAccountChange(
                merchantSn, acquirer, oldAccountInfo, newAccountInfo);

        // Then: 验证结果
        assertFalse("应该拦截变更", result.isAllowed());
        assertTrue("ruleMatched 应为 true", result.isRuleMatched());
        assertTrue("消息应该包含默认规则名称", result.getMessage().contains("未命名规则"));

        log.info("✅ 测试场景7通过");
    }

    /**
     * 测试场景8：异常处理
     * 预期：抛出ContractBizException
     */
    @Test(expected = com.wosai.upay.merchant.contract.exception.ContractBizException.class)
    public void testValidateAccountChange_Exception() {
        log.info("=== 测试场景8：异常处理 ===");

        // Given: DAO抛出异常
        when(accountChangeRuleDAO.getByAcquirer(acquirer))
                .thenThrow(new RuntimeException("Database connection failed"));

        // When & Then: 应该抛出业务异常
        validationService.validateAccountChange(merchantSn, acquirer, oldAccountInfo, newAccountInfo);
    }

    /**
     * 测试场景21：白名单机制 - 商户在白名单中
     * 预期：直接允许变更，不受规则限制
     */
    @Test
    public void testValidateAccountChange_WhitelistAllowed() {
        log.info("=== 测试场景21：白名单机制 - 商户在白名单中 ===");

        // Given: 商户在白名单中
        String whitelistedMerchantSn = "WHITELISTED_MERCHANT_001";
        when(applicationApolloConfig.getAcquirerChangeCardWhitelist())
                .thenReturn(java.util.Arrays.asList(whitelistedMerchantSn));

        // When: 执行校验
        AccountChangeValidationResult result = validationService.validateAccountChange(
                whitelistedMerchantSn, acquirer, oldAccountInfo, newAccountInfo);

        // Then: 验证结果
        assertTrue("应该允许变更", result.isAllowed());
        assertFalse("应该标识无规则限制", result.isHasRuleRestriction());
        assertFalse("应该标识规则未匹配", result.isRuleMatched());
        assertTrue("消息应该包含白名单提示", result.getMessage().contains("白名单"));

        // 验证不会查询规则和历史记录
        verify(accountChangeRuleDAO, never()).getByAcquirer(any());
        verifyNoMoreInteractions(accountChangeRecordDAO, ruleEngine);

        log.info("✅ 测试场景21通过");
    }

    /**
     * 测试场景22：白名单机制 - 商户不在白名单中
     * 预期：按正常流程处理
     */
    @Test
    public void testValidateAccountChange_WhitelistNotInList() {
        log.info("=== 测试场景22：白名单机制 - 商户不在白名单中 ===");

        // Given: 商户不在白名单中，且存在允许变更的规则
        when(accountChangeRuleDAO.getByAcquirer(acquirer)).thenReturn(Optional.empty());

        // When: 执行校验
        AccountChangeValidationResult result = validationService.validateAccountChange(
                merchantSn, acquirer, oldAccountInfo, newAccountInfo);

        // Then: 验证结果 - 按正常无规则处理
        assertTrue("应该允许变更", result.isAllowed());
        assertFalse("应该标识无规则限制", result.isHasRuleRestriction());
        assertEquals("消息应该正确", "无换卡限制规则，可以进行变更", result.getMessage());

        log.info("✅ 测试场景22通过");
    }

    /**
     * 测试场景23：白名单机制 - Apollo配置异常
     * 预期：按正常流程处理，不阻断业务流程
     */
    @Test
    public void testValidateAccountChange_WhitelistConfigException() {
        log.info("=== 测试场景23：白名单机制 - Apollo配置异常 ===");

        // Given: Apollo配置异常
        when(applicationApolloConfig.getAcquirerChangeCardWhitelist())
                .thenThrow(new RuntimeException("Apollo配置读取失败"));
        when(accountChangeRuleDAO.getByAcquirer(acquirer)).thenReturn(Optional.empty());

        // When: 执行校验
        AccountChangeValidationResult result = validationService.validateAccountChange(
                merchantSn, acquirer, oldAccountInfo, newAccountInfo);

        // Then: 验证结果 - 按正常流程处理
        assertTrue("应该允许变更", result.isAllowed());
        assertFalse("应该标识无规则限制", result.isHasRuleRestriction());

        log.info("✅ 测试场景23通过");
    }

    /**
     * 创建零限制规则
     */
    private AcquirerAccountChangeRuleDO createZeroLimitRule(String ruleName) {
        AcquirerAccountChangeRuleDO rule = new AcquirerAccountChangeRuleDO();
        rule.setId(1L);
        rule.setAcquirer(acquirer);
        rule.setRuleName(ruleName);
        rule.setMaxCount(0);
        rule.setPeriodDays(30);
        rule.setRuleLogic("{\"ruleName\":\"" + ruleName + "\",\"conditions\":[{\"fieldPath\":\"new_account_info.bankName\",\"operator\":\"NOT_EQUALS\",\"compareFieldPath\":\"old_account_info.bankName\"}]}");
        rule.setCtime(new Date());
        rule.setMtime(new Date());
        return rule;
    }

    /**
     * 创建次数限制规则
     */
    private AcquirerAccountChangeRuleDO createCountLimitRule(String ruleName, int maxCount, int periodDays) {
        AcquirerAccountChangeRuleDO rule = new AcquirerAccountChangeRuleDO();
        rule.setId(2L);
        rule.setAcquirer(acquirer);
        rule.setRuleName(ruleName);
        rule.setMaxCount(maxCount);
        rule.setPeriodDays(periodDays);
        rule.setRuleLogic("{\"ruleName\":\"" + ruleName + "\",\"conditions\":[{\"fieldPath\":\"new_account_info.cardNumber\",\"operator\":\"NOT_EQUALS\",\"compareFieldPath\":\"old_account_info.cardNumber\"}]}");
        rule.setCtime(new Date());
        rule.setMtime(new Date());
        return rule;
    }

    /**
     * 创建完整的账户变更记录
     */
    private com.wosai.upay.job.refactor.model.entity.AcquirerAccountChangeRecordDO createAccountChangeRecord(
            Long id, String merchantSn, String acquirer, String oldAccountJson, String newAccountJson, int daysAgo) {
        com.wosai.upay.job.refactor.model.entity.AcquirerAccountChangeRecordDO record =
                new com.wosai.upay.job.refactor.model.entity.AcquirerAccountChangeRecordDO();
        record.setId(id);
        record.setMerchantSn(merchantSn);
        record.setAcquirer(acquirer);
        record.setOldAccountInfo(oldAccountJson);
        record.setNewAccountInfo(newAccountJson);
        record.setCompleteTime(new Date(System.currentTimeMillis() - daysAgo * 24 * 60 * 60 * 1000L));
        record.setStatus(AcquirerAccountChangeStatusEnum.SUCCESS.getValue());
        record.setCtime(new Date(System.currentTimeMillis() - daysAgo * 24 * 60 * 60 * 1000L));
        record.setMtime(new Date());
        return record;
    }

    /* ====== 测试 calculateNextChangeTime 方法 ====== */

    /**
     * 测试场景9：无规则配置时返回当前时间
     */
    @Test
    public void testCalculateNextChangeTime_NoRule() {
        log.info("=== 测试场景9：无规则配置时返回当前时间 ===");

        // Given: 无规则配置
        when(accountChangeRuleDAO.getByAcquirer(acquirer)).thenReturn(Optional.empty());

        // When: 计算下次变更时间
        Date nextChangeTime = validationService.calculateNextChangeTime(merchantSn, acquirer);

        // Then: 应该返回当前时间
        assertNotNull("返回时间不应为空", nextChangeTime);
        log.info("✅ 测试场景9通过");
    }

    /**
     * 测试场景10：零限制规则时返回当前时间
     */
    @Test
    public void testCalculateNextChangeTime_ZeroLimitRule() {
        log.info("=== 测试场景10：零限制规则时返回当前时间 ===");

        // Given: 零限制规则
        AcquirerAccountChangeRuleDO rule = createZeroLimitRule("零限制测试");
        when(accountChangeRuleDAO.getByAcquirer(acquirer)).thenReturn(Optional.of(rule));

        // When: 计算下次变更时间
        Date nextChangeTime = validationService.calculateNextChangeTime(merchantSn, acquirer);

        // Then: 应该返回当前时间
        assertNotNull("返回时间不应为空", nextChangeTime);
        log.info("✅ 测试场景10通过");
    }

    /**
     * 测试场景11：无历史记录时返回当前时间
     */
    @Test
    public void testCalculateNextChangeTime_NoHistoryRecords() {
        log.info("=== 测试场景11：无历史记录时返回当前时间 ===");

        // Given: 次数限制规则，但无历史记录
        AcquirerAccountChangeRuleDO rule = createCountLimitRule("次数限制测试", 3, 30);
        when(accountChangeRuleDAO.getByAcquirer(acquirer)).thenReturn(Optional.of(rule));

        // Mock 空记录列表
        when(accountChangeRecordDAO.findSuccessRecords(anyString(), anyString(), any(Date.class), any(Date.class)))
                .thenReturn(java.util.Collections.emptyList());
        when(ruleEngine.evaluate(any(), any())).thenReturn(true);

        // When: 计算下次变更时间
        Date nextChangeTime = validationService.calculateNextChangeTime(merchantSn, acquirer);

        // Then: 应该返回当前时间
        assertNotNull("返回时间不应为空", nextChangeTime);
        log.info("✅ 测试场景11通过");
    }

    /**
     * 测试场景12：当前次数小于最大次数，基于最早记录计算（含+1天逻辑）
     */
    @Test
    public void testCalculateNextChangeTime_CountLessThanMax() {
        log.info("=== 测试场景12：当前次数小于最大次数，基于最早记录计算（含+1天逻辑） ===");

        // Given: 限制3次/30天，当前只有1次记录
        AcquirerAccountChangeRuleDO rule = createCountLimitRule("次数限制测试", 3, 30);
        when(accountChangeRuleDAO.getByAcquirer(acquirer)).thenReturn(Optional.of(rule));

        // 创建1条10天前的记录
        com.wosai.upay.job.refactor.model.entity.AcquirerAccountChangeRecordDO record =
                createAccountChangeRecord(1L, merchantSn, acquirer,
                        "{\"cardNumber\":\"************\"}",
                        "{\"cardNumber\":\"************\"}", 10);

        when(accountChangeRecordDAO.findSuccessRecords(anyString(), anyString(), any(Date.class), any(Date.class)))
                .thenReturn(java.util.Arrays.asList(record));
        when(ruleEngine.evaluate(any(), any())).thenReturn(true);

        // When: 计算下次变更时间
        Date nextChangeTime = validationService.calculateNextChangeTime(merchantSn, acquirer);

        // Then: 应该返回一个有效的时间
        assertNotNull("返回时间不应为空", nextChangeTime);

        // 验证：计算出的时间应该是基于记录的完成时间
        // 由于记录是10天前的，加上31天（30+1）应该在未来
        assertTrue("下次变更时间应该在未来", nextChangeTime.after(new Date()));

        log.info("✅ 测试场景12通过");
    }

    /**
     * 测试场景13：当前次数等于最大次数，计算滑动窗口（验证min逻辑）
     */
    @Test
    public void testCalculateNextChangeTime_CountEqualsMax() {
        log.info("=== 测试场景13：当前次数等于最大次数，计算滑动窗口（验证min逻辑） ===");

        // Given: 限制2次/30天，当前有2次记录
        AcquirerAccountChangeRuleDO rule = createCountLimitRule("次数限制测试", 2, 30);
        when(accountChangeRuleDAO.getByAcquirer(acquirer)).thenReturn(Optional.of(rule));

        // 创建2条记录：5天前和2天前
        com.wosai.upay.job.refactor.model.entity.AcquirerAccountChangeRecordDO record1 =
                createAccountChangeRecord(1L, merchantSn, acquirer,
                        "{\"cardNumber\":\"************\"}",
                        "{\"cardNumber\":\"************\"}", 5);
        com.wosai.upay.job.refactor.model.entity.AcquirerAccountChangeRecordDO record2 =
                createAccountChangeRecord(2L, merchantSn, acquirer,
                        "{\"cardNumber\":\"************\"}",
                        "{\"cardNumber\":\"************\"}", 2);

        when(accountChangeRecordDAO.findSuccessRecords(anyString(), anyString(), any(Date.class), any(Date.class)))
                .thenReturn(java.util.Arrays.asList(record1, record2));
        when(ruleEngine.evaluate(any(), any())).thenReturn(true);

        // When: 计算下次变更时间
        Date nextChangeTime = validationService.calculateNextChangeTime(merchantSn, acquirer);

        // Then: 应该基于最后2条记录的最小完成时间（5天前）+ 30 + 1 = 26天后
        assertNotNull("返回时间不应为空", nextChangeTime);
        assertTrue("下次变更时间应该在未来", nextChangeTime.after(new Date()));

        // 验证：计算应该基于最后2条记录中的最小完成时间
        Date minCompleteTime = record1.getCompleteTime(); // 5天前的记录是最小完成时间

        // 验证日期级匹配（忽略时分秒）
        Calendar expectedCal = Calendar.getInstance();
        expectedCal.setTime(minCompleteTime);
        expectedCal.add(Calendar.DAY_OF_MONTH, 31); // periodDays(30) + 1
        expectedCal.set(Calendar.HOUR_OF_DAY, 0);
        expectedCal.set(Calendar.MINUTE, 0);
        expectedCal.set(Calendar.SECOND, 0);
        expectedCal.set(Calendar.MILLISECOND, 0);
        Date expectedTime = expectedCal.getTime();
        
        // 只比较日期部分，忽略具体时间
        Calendar actualCal = Calendar.getInstance();
        actualCal.setTime(nextChangeTime);
        actualCal.set(Calendar.HOUR_OF_DAY, 0);
        actualCal.set(Calendar.MINUTE, 0);
        actualCal.set(Calendar.SECOND, 0);
        actualCal.set(Calendar.MILLISECOND, 0);
        
        assertEquals("计算时间应该日期匹配", expectedTime, actualCal.getTime());

        log.info("✅ 测试场景13通过");
    }

    /**
     * 测试场景14：当前次数超过最大次数，计算滑动窗口
     */
    @Test
    public void testCalculateNextChangeTime_CountExceedsMax() {
        log.info("=== 测试场景14：当前次数超过最大次数，计算滑动窗口 ===");

        // Given: 限制2次/30天，当前有3次记录
        AcquirerAccountChangeRuleDO rule = createCountLimitRule("次数限制测试", 2, 30);
        when(accountChangeRuleDAO.getByAcquirer(acquirer)).thenReturn(Optional.of(rule));

        // 创建3条记录：15天前、10天前、5天前
        com.wosai.upay.job.refactor.model.entity.AcquirerAccountChangeRecordDO record1 =
                createAccountChangeRecord(1L, merchantSn, acquirer,
                        "{\"cardNumber\":\"************\"}",
                        "{\"cardNumber\":\"************\"}", 15);
        com.wosai.upay.job.refactor.model.entity.AcquirerAccountChangeRecordDO record2 =
                createAccountChangeRecord(2L, merchantSn, acquirer,
                        "{\"cardNumber\":\"************\"}",
                        "{\"cardNumber\":\"************\"}", 10);
        com.wosai.upay.job.refactor.model.entity.AcquirerAccountChangeRecordDO record3 =
                createAccountChangeRecord(3L, merchantSn, acquirer,
                        "{\"cardNumber\":\"************\"}",
                        "{\"cardNumber\":\"************\"}", 5);

        when(accountChangeRecordDAO.findSuccessRecords(anyString(), anyString(), any(Date.class), any(Date.class)))
                .thenReturn(java.util.Arrays.asList(record1, record2, record3));
        when(ruleEngine.evaluate(any(), any())).thenReturn(true);

        // When: 计算下次变更时间
        Date nextChangeTime = validationService.calculateNextChangeTime(merchantSn, acquirer);

        // Then: 应该基于最后2条记录的最小完成时间（10天前）+ 30 + 1 = 21天后
        assertNotNull("返回时间不应为空", nextChangeTime);
        assertTrue("下次变更时间应该在未来", nextChangeTime.after(new Date()));

        // 验证：最小完成时间应该是最后2条记录中的最小时间（10天前的记录）
        Date minCompleteTime = record2.getCompleteTime(); // 10天前的记录是最小完成时间

        // 验证日期级匹配（忽略时分秒）
        Calendar expectedCal = Calendar.getInstance();
        expectedCal.setTime(minCompleteTime);
        expectedCal.add(Calendar.DAY_OF_MONTH, 31); // periodDays(30) + 1
        expectedCal.set(Calendar.HOUR_OF_DAY, 0);
        expectedCal.set(Calendar.MINUTE, 0);
        expectedCal.set(Calendar.SECOND, 0);
        expectedCal.set(Calendar.MILLISECOND, 0);
        Date expectedTime = expectedCal.getTime();
        
        // 只比较日期部分，忽略具体时间
        Calendar actualCal = Calendar.getInstance();
        actualCal.setTime(nextChangeTime);
        actualCal.set(Calendar.HOUR_OF_DAY, 0);
        actualCal.set(Calendar.MINUTE, 0);
        actualCal.set(Calendar.SECOND, 0);
        actualCal.set(Calendar.MILLISECOND, 0);
        
        assertEquals("计算时间应该日期匹配", expectedTime, actualCal.getTime());

        log.info("✅ 测试场景14通过");
    }

    /**
     * 测试场景15：计算出的时间已过期时返回当前时间
     */
    @Test
    public void testCalculateNextChangeTime_ExpiredTime() {
        log.info("=== 测试场景15：计算出的时间已过期时返回当前时间 ===");

        // Given: 限制2次/30天，当前有1条40天前的记录
        AcquirerAccountChangeRuleDO rule = createCountLimitRule("次数限制测试", 2, 30);
        when(accountChangeRuleDAO.getByAcquirer(acquirer)).thenReturn(Optional.of(rule));

        // 创建1条40天前的记录（已过期）
        com.wosai.upay.job.refactor.model.entity.AcquirerAccountChangeRecordDO record =
                createAccountChangeRecord(1L, merchantSn, acquirer,
                        "{\"cardNumber\":\"************\"}",
                        "{\"cardNumber\":\"************\"}", 40);

        when(accountChangeRecordDAO.findSuccessRecords(anyString(), anyString(), any(Date.class), any(Date.class)))
                .thenReturn(java.util.Arrays.asList(record));
        when(ruleEngine.evaluate(any(), any())).thenReturn(true);

        // When: 计算下次变更时间
        Date nextChangeTime = validationService.calculateNextChangeTime(merchantSn, acquirer);

        // Then: 应该返回当前时间（因为40天前+30+1=9天前，已过期）
        assertNotNull("返回时间不应为空", nextChangeTime);

        log.info("✅ 测试场景15通过");
    }

    /**
     * 测试场景16：带providerMerchantId参数的计算（优化版）
     */
    @Test
    public void testCalculateNextChangeTime_WithProviderMerchantId() {
        log.info("=== 测试场景16：带providerMerchantId参数的计算（优化版） ===");

        // Given: 限制2次/30天，当前有1次记录
        AcquirerAccountChangeRuleDO rule = createCountLimitRule("次数限制测试", 2, 30);
        when(accountChangeRuleDAO.getByAcquirer(acquirer)).thenReturn(Optional.of(rule));

        // 创建1条记录
        com.wosai.upay.job.refactor.model.entity.AcquirerAccountChangeRecordDO record =
                createAccountChangeRecord(1L, merchantSn, acquirer,
                        "{\"cardNumber\":\"************\"}",
                        "{\"cardNumber\":\"************\"}", 10);

        when(accountChangeRecordDAO.findSuccessRecords(anyString(), anyString(), anyString(), any(Date.class), any(Date.class)))
                .thenReturn(java.util.Arrays.asList(record));
        when(ruleEngine.evaluate(any(), any())).thenReturn(true);

        // When: 计算下次变更时间（带providerMerchantId）
        Date nextChangeTime = validationService.calculateNextChangeTime(merchantSn, acquirer, "PROVIDER_123");

        // Then: 应该基于最早记录(10天前) + 30 + 1 = 21天后
        assertNotNull("返回时间不应为空", nextChangeTime);
        assertTrue("下次变更时间应该在未来", nextChangeTime.after(new Date()));

        // 验证日期级匹配（忽略时分秒）
        Calendar expectedCal = Calendar.getInstance();
        expectedCal.setTime(record.getCompleteTime());
        expectedCal.add(Calendar.DAY_OF_MONTH, 31); // periodDays(30) + 1
        expectedCal.set(Calendar.HOUR_OF_DAY, 0);
        expectedCal.set(Calendar.MINUTE, 0);
        expectedCal.set(Calendar.SECOND, 0);
        expectedCal.set(Calendar.MILLISECOND, 0);
        Date expectedTime = expectedCal.getTime();
        
        // 只比较日期部分，忽略具体时间
        Calendar actualCal = Calendar.getInstance();
        actualCal.setTime(nextChangeTime);
        actualCal.set(Calendar.HOUR_OF_DAY, 0);
        actualCal.set(Calendar.MINUTE, 0);
        actualCal.set(Calendar.SECOND, 0);
        actualCal.set(Calendar.MILLISECOND, 0);
        
        assertEquals("计算时间应该日期匹配", expectedTime, actualCal.getTime());

        log.info("✅ 测试场景16通过");
    }

    /**
     * 测试场景17：验证subList边界条件（验证最后maxCount条记录）
     */
    @Test
    public void testCalculateNextChangeTime_SubListBoundary() {
        log.info("=== 测试场景17：验证subList边界条件（验证最后maxCount条记录） ===");

        // Given: 限制3次/30天，当前有5次记录
        AcquirerAccountChangeRuleDO rule = createCountLimitRule("次数限制测试", 3, 30);
        when(accountChangeRuleDAO.getByAcquirer(acquirer)).thenReturn(Optional.of(rule));

        // 创建5条记录：20天前、15天前、10天前、7天前、3天前
        com.wosai.upay.job.refactor.model.entity.AcquirerAccountChangeRecordDO record1 =
                createAccountChangeRecord(1L, merchantSn, acquirer, "{\"cardNumber\":\"111\"}", "{\"cardNumber\":\"222\"}", 20);
        com.wosai.upay.job.refactor.model.entity.AcquirerAccountChangeRecordDO record2 =
                createAccountChangeRecord(2L, merchantSn, acquirer, "{\"cardNumber\":\"222\"}", "{\"cardNumber\":\"333\"}", 15);
        com.wosai.upay.job.refactor.model.entity.AcquirerAccountChangeRecordDO record3 =
                createAccountChangeRecord(3L, merchantSn, acquirer, "{\"cardNumber\":\"333\"}", "{\"cardNumber\":\"444\"}", 10);
        com.wosai.upay.job.refactor.model.entity.AcquirerAccountChangeRecordDO record4 =
                createAccountChangeRecord(4L, merchantSn, acquirer, "{\"cardNumber\":\"444\"}", "{\"cardNumber\":\"555\"}", 7);
        com.wosai.upay.job.refactor.model.entity.AcquirerAccountChangeRecordDO record5 =
                createAccountChangeRecord(5L, merchantSn, acquirer, "{\"cardNumber\":\"555\"}", "{\"cardNumber\":\"666\"}", 3);

        when(accountChangeRecordDAO.findSuccessRecords(anyString(), anyString(), any(Date.class), any(Date.class)))
                .thenReturn(java.util.Arrays.asList(record1, record2, record3, record4, record5));
        when(ruleEngine.evaluate(any(), any())).thenReturn(true);

        // When: 计算下次变更时间
        Date nextChangeTime = validationService.calculateNextChangeTime(merchantSn, acquirer);

        // Then: 应该基于最后3条记录中的最小完成时间计算
        assertNotNull("返回时间不应为空", nextChangeTime);
        assertTrue("下次变更时间应该在未来", nextChangeTime.after(new Date()));

        // 验证：最后3条记录是record3(10天前)、record4(7天前)、record5(3天前)
        // 最小完成时间是record3的10天前
        Date minCompleteTimeInLast3 = record3.getCompleteTime(); // 10天前的记录

        // 验证计算逻辑：最小完成时间 + 31天（30+1）
        Calendar expectedCal = Calendar.getInstance();
        expectedCal.setTime(minCompleteTimeInLast3);
        expectedCal.add(Calendar.DAY_OF_MONTH, 31);
        expectedCal.set(Calendar.HOUR_OF_DAY, 0);
        expectedCal.set(Calendar.MINUTE, 0);
        expectedCal.set(Calendar.SECOND, 0);
        expectedCal.set(Calendar.MILLISECOND, 0);
        Date expectedTime = expectedCal.getTime();
        
        // 验证实际计算结果与预期一致（允许1天的误差）
        long expectedDays = expectedTime.getTime() / (24 * 60 * 60 * 1000);
        long actualDays = nextChangeTime.getTime() / (24 * 60 * 60 * 1000);
        
        assertTrue("计算时间应该接近预期", Math.abs(expectedDays - actualDays) <= 1);
        log.info("期望时间: {}, 实际时间: {}", expectedTime, nextChangeTime);

        log.info("✅ 测试场景17通过");
    }

    /**
     * 测试场景18：计算逻辑验证 - 小于最大次数时的计算
     */
    @Test
    public void testCalculateNextChangeTime_LogicVerification_BelowLimit() {
        log.info("=== 测试场景18：计算逻辑验证 - 小于最大次数时的计算 ===");

        // Given: 30天内最多3次的规则，当前只有2次记录
        AcquirerAccountChangeRuleDO rule = createCountLimitRule("逻辑验证规则", 3, 30);
        when(accountChangeRuleDAO.getByAcquirer(acquirer)).thenReturn(Optional.of(rule));
        when(ruleEngine.evaluate(any(), eq(rule.getRuleLogic()))).thenReturn(true);

        // 模拟两次历史记录：5天前和2天前
        when(accountChangeRecordDAO.findSuccessRecords(eq(merchantSn), eq(acquirer), any(Date.class), any(Date.class)))
                .thenReturn(Arrays.asList(
                        createAccountChangeRecord(1L, merchantSn, acquirer,
                                "{\"cardNumber\":\"6217000010001111111\"}",
                                "{\"cardNumber\":\"6217000010002222222\"}", 5),
                        createAccountChangeRecord(2L, merchantSn, acquirer,
                                "{\"cardNumber\":\"6217000010002222222\"}",
                                "{\"cardNumber\":\"6217000010003333333\"}", 2)
                ));

        // When: 计算下次变更时间
        Date nextChangeTime = validationService.calculateNextChangeTime(merchantSn, acquirer, null);

        // Then: 应该基于最早记录（5天前）+ 30天 + 1天 = 26天后可以变更
        assertNotNull("下次变更时间不应为空", nextChangeTime);
        // 验证时间大致正确（考虑到当前小于限制，应该基于最早记录计算）
        Date now = new Date();
        assertTrue("下次变更时间应该是未来时间", nextChangeTime.after(now));

        log.info("下次变更时间: {}", nextChangeTime);
        log.info("✅ 测试场景18通过");
    }

    /**
     * 测试场景19：计算逻辑验证 - 等于最大次数时的计算
     */
    @Test
    public void testCalculateNextChangeTime_LogicVerification_AtLimit() {
        log.info("=== 测试场景19：计算逻辑验证 - 等于最大次数时的计算 ===");

        // Given: 30天内最多3次的规则，当前恰好有3次记录
        AcquirerAccountChangeRuleDO rule = createCountLimitRule("逻辑验证规则", 3, 30);
        when(accountChangeRuleDAO.getByAcquirer(acquirer)).thenReturn(Optional.of(rule));
        when(ruleEngine.evaluate(any(), eq(rule.getRuleLogic()))).thenReturn(true);

        // 模拟三次历史记录：5天前、3天前、1天前
        when(accountChangeRecordDAO.findSuccessRecords(eq(merchantSn), eq(acquirer), any(Date.class), any(Date.class)))
                .thenReturn(Arrays.asList(
                        createAccountChangeRecord(1L, merchantSn, acquirer,
                                "{\"cardNumber\":\"6217000010001111111\"}",
                                "{\"cardNumber\":\"6217000010002222222\"}", 5),
                        createAccountChangeRecord(2L, merchantSn, acquirer,
                                "{\"cardNumber\":\"6217000010002222222\"}",
                                "{\"cardNumber\":\"6217000010003333333\"}", 3),
                        createAccountChangeRecord(3L, merchantSn, acquirer,
                                "{\"cardNumber\":\"6217000010003333333\"}",
                                "{\"cardNumber\":\"6217000010004444444\"}", 1)
                ));

        // When: 计算下次变更时间
        Date nextChangeTime = validationService.calculateNextChangeTime(merchantSn, acquirer, null);

        // Then: 应该基于最早记录（5天前）+ 30天 + 1天 = 26天后可以变更
        assertNotNull("下次变更时间不应为空", nextChangeTime);
        Date now = new Date();
        assertTrue("下次变更时间应该是未来时间", nextChangeTime.after(now));

        log.info("下次变更时间: {}", nextChangeTime);
        log.info("✅ 测试场景19通过");
    }

    /**
     * 测试场景20：计算逻辑验证 - 超过最大次数时的计算
     */
    @Test
    public void testCalculateNextChangeTime_LogicVerification_OverLimit() {
        log.info("=== 测试场景20：计算逻辑验证 - 超过最大次数时的计算 ===");

        // Given: 30天内最多3次的规则，当前有5次记录
        AcquirerAccountChangeRuleDO rule = createCountLimitRule("逻辑验证规则", 3, 30);
        when(accountChangeRuleDAO.getByAcquirer(acquirer)).thenReturn(Optional.of(rule));
        when(ruleEngine.evaluate(any(), eq(rule.getRuleLogic()))).thenReturn(true);

        // 模拟五次历史记录：6天前、5天前、3天前、2天前、1天前
        when(accountChangeRecordDAO.findSuccessRecords(eq(merchantSn), eq(acquirer), any(Date.class), any(Date.class)))
                .thenReturn(Arrays.asList(
                        createAccountChangeRecord(1L, merchantSn, acquirer,
                                "{\"cardNumber\":\"6217000010001111111\"}",
                                "{\"cardNumber\":\"6217000010002222222\"}", 6),
                        createAccountChangeRecord(2L, merchantSn, acquirer,
                                "{\"cardNumber\":\"6217000010002222222\"}",
                                "{\"cardNumber\":\"6217000010003333333\"}", 5),
                        createAccountChangeRecord(3L, merchantSn, acquirer,
                                "{\"cardNumber\":\"6217000010003333333\"}",
                                "{\"cardNumber\":\"6217000010004444444\"}", 3),
                        createAccountChangeRecord(4L, merchantSn, acquirer,
                                "{\"cardNumber\":\"6217000010004444444\"}",
                                "{\"cardNumber\":\"6217000010005555555\"}", 2),
                        createAccountChangeRecord(5L, merchantSn, acquirer,
                                "{\"cardNumber\":\"6217000010005555555\"}",
                                "{\"cardNumber\":\"6217000010006666666\"}", 1)
                ));

        // When: 计算下次变更时间
        Date nextChangeTime = validationService.calculateNextChangeTime(merchantSn, acquirer, null);

        // Then: 应该基于第(5-3+1)=3条记录（3天前）+ 30天 + 1天 = 28天后可以变更
        assertNotNull("下次变更时间不应为空", nextChangeTime);
        Date now = new Date();
        assertTrue("下次变更时间应该是未来时间", nextChangeTime.after(now));

        log.info("下次变更时间: {}", nextChangeTime);
        log.info("✅ 测试场景20通过");
    }
}
