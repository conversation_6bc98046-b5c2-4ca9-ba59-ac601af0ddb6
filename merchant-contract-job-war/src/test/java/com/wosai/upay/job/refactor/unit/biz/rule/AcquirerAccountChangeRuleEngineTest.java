package com.wosai.upay.job.refactor.unit.biz.rule;

import com.alibaba.fastjson.JSON;
import com.wosai.upay.job.refactor.biz.rule.AcquirerAccountChangeRuleEngine;
import com.wosai.upay.job.refactor.model.bo.RuleConditionBO;
import com.wosai.upay.job.refactor.model.entity.AcquirerAccountChangeRecordDO;
import com.wosai.upay.job.refactor.model.enums.LogicOperatorEnum;
import com.wosai.upay.job.refactor.model.enums.RuleOperatorEnum;
import com.wosai.upay.job.dto.AccountChangeValidationResult;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.Date;

import static org.junit.Assert.*;

/**
 * AcquirerAccountChangeRuleEngine 单元测试
 * 测试6种规则场景的处理逻辑
 *
 * <AUTHOR>
 * @since 2025-01-22
 */
@RunWith(MockitoJUnitRunner.class)
@Slf4j
public class AcquirerAccountChangeRuleEngineTest {

    private AcquirerAccountChangeRuleEngine ruleEngine;
    private AcquirerAccountChangeRecordDO testRecord;

    @Before
    public void setUp() {
        ruleEngine = new AcquirerAccountChangeRuleEngine();
        
        // 准备测试数据
        testRecord = new AcquirerAccountChangeRecordDO();
        testRecord.setMerchantSn("test_merchant_123");
        testRecord.setAcquirer("test_acquirer");
        testRecord.setStatus(1);
        
        // 构建老账户信息JSON
        String oldAccountInfo = "{"
                + "\"cardNumber\": \"****************\","
                + "\"type\": 1,"
                + "\"holder\": \"张三\","
                + "\"bankName\": \"工商银行\","
                + "\"openingBank\": \"工商银行北京分行\","
                + "\"clearingBank\": \"************\""
                + "}";
        testRecord.setOldAccountInfo(oldAccountInfo);
        
        // 构建新账户信息JSON - 卡号和银行名都变更了
        String newAccountInfo = "{"
                + "\"cardNumber\": \"****************\","
                + "\"type\": 1,"
                + "\"holder\": \"张三\","
                + "\"bankName\": \"建设银行\","
                + "\"openingBank\": \"建设银行北京分行\","
                + "\"clearingBank\": \"************\""
                + "}";
        testRecord.setNewAccountInfo(newAccountInfo);
        
        testRecord.setCtime(new Date());
    }

    /**
     * 测试场景1：字段与固定值比较
     * 规则：new_account_info.type = 1
     */
    @Test
    public void testRule1_FieldToFixedValueComparison() {
        // Given
        RuleConditionBO rule = new RuleConditionBO();
        rule.setRuleName("账户类型匹配规则");        
        RuleConditionBO condition = new RuleConditionBO();
        condition.setFieldPath("new_account_info.type");
        condition.setOperator(RuleOperatorEnum.EQUALS);
        condition.setCompareValue(1);
        
        rule.setConditions(Arrays.asList(condition));
        String ruleJson = JSON.toJSONString(rule);
        
        // When
        boolean result = ruleEngine.evaluate(testRecord, ruleJson);
        
        // Then
        assertTrue("账户类型为1，应该匹配", result);
        
        // 测试不匹配的情况
        condition.setCompareValue(2);
        String ruleJson2 = JSON.toJSONString(rule);
        boolean result2 = ruleEngine.evaluate(testRecord, ruleJson2);
        assertFalse("账户类型不为2，应该不匹配", result2);
    }

    /**
     * 测试场景2：字段与字段比较
     * 规则：new_account_info.cardNumber != old_account_info.cardNumber
     */
    @Test
    public void testRule2_FieldToFieldComparison() {
        // Given
        RuleConditionBO rule = new RuleConditionBO();
        rule.setRuleName("银行卡号变更检测规则");
        
        RuleConditionBO condition = new RuleConditionBO();
        condition.setFieldPath("new_account_info.cardNumber");
        condition.setOperator(RuleOperatorEnum.NOT_EQUALS);
        condition.setCompareFieldPath("old_account_info.cardNumber");
        
        rule.setConditions(Arrays.asList(condition));
        String ruleJson = JSON.toJSONString(rule);
        
        // When
        boolean result = ruleEngine.evaluate(testRecord, ruleJson);
        
        // Then
        assertTrue("卡号已变更，应该匹配", result);
    }

    /**
     * 测试场景3：IN操作
     * 规则：new_account_info.type IN [1, 2]
     */
    @Test
    public void testRule3_InOperation() {
        // Given
        RuleConditionBO rule = new RuleConditionBO();
        rule.setRuleName("账户类型白名单规则");
        
        RuleConditionBO condition = new RuleConditionBO();
        condition.setFieldPath("new_account_info.type");
        condition.setOperator(RuleOperatorEnum.IN);
        condition.setCompareValueList(Arrays.asList(1, 2));
        
        rule.setConditions(Arrays.asList(condition));
        String ruleJson = JSON.toJSONString(rule);
        
        // When
        boolean result = ruleEngine.evaluate(testRecord, ruleJson);
        
        // Then
        assertTrue("账户类型1在[1,2]中，应该匹配", result);
        
        // 测试不在列表中的情况
        condition.setCompareValueList(Arrays.asList(3, 4));
        String ruleJson2 = JSON.toJSONString(rule);
        boolean result2 = ruleEngine.evaluate(testRecord, ruleJson2);
        assertFalse("账户类型1不在[3,4]中，应该不匹配", result2);
    }

    /**
     * 测试场景4：多个条件的AND组合（默认）
     * 规则：new_account_info.cardNumber != old_account_info.cardNumber 
     *      AND new_account_info.bankName != old_account_info.bankName
     */
    @Test
    public void testRule4_MultipleConditionsAnd() {
        // Given
        RuleConditionBO rule = new RuleConditionBO();
        rule.setRuleName("卡号和银行名同时变更规则");

        
        RuleConditionBO condition1 = new RuleConditionBO();
        condition1.setFieldPath("new_account_info.cardNumber");
        condition1.setOperator(RuleOperatorEnum.NOT_EQUALS);
        condition1.setCompareFieldPath("old_account_info.cardNumber");
        
        RuleConditionBO condition2 = new RuleConditionBO();
        condition2.setFieldPath("new_account_info.bankName");
        condition2.setOperator(RuleOperatorEnum.NOT_EQUALS);
        condition2.setCompareFieldPath("old_account_info.bankName");
        
        rule.setConditions(Arrays.asList(condition1, condition2));
        String ruleJson = JSON.toJSONString(rule);
        
        // When
        boolean result = ruleEngine.evaluate(testRecord, ruleJson);
        
        // Then
        assertTrue("卡号和银行名都变更了，AND条件应该为真", result);
    }

    /**
     * 测试场景5：OR逻辑组合（嵌套结构）
     * 规则：new_account_info.cardNumber != old_account_info.cardNumber 
     *      OR new_account_info.bankName != old_account_info.bankName
     */
    @Test
    public void testRule5_OrLogicWithNestedStructure() {
        // Given
        RuleConditionBO rule = new RuleConditionBO();

        rule.setRuleName("卡号或银行名变更规则");

        
        // 创建OR逻辑组
        RuleConditionBO orGroup = new RuleConditionBO();
        orGroup.setLogic(LogicOperatorEnum.OR); // orGroup内部条件使用OR逻辑
        
        RuleConditionBO condition1 = new RuleConditionBO();
        condition1.setFieldPath("new_account_info.cardNumber");
        condition1.setOperator(RuleOperatorEnum.NOT_EQUALS);
        condition1.setCompareFieldPath("old_account_info.cardNumber");
        
        RuleConditionBO condition2 = new RuleConditionBO();
        condition2.setFieldPath("new_account_info.bankName");
        condition2.setOperator(RuleOperatorEnum.NOT_EQUALS);
        condition2.setCompareFieldPath("old_account_info.bankName");
        
        orGroup.setConditions(Arrays.asList(condition1, condition2));
        rule.setConditions(Arrays.asList(orGroup));
        String ruleJson = JSON.toJSONString(rule);
        
        // When
        boolean result = ruleEngine.evaluate(testRecord, ruleJson);
        
        // Then
        assertTrue("卡号或银行名变更，OR条件应该为真", result);
    }

    /**
     * 测试场景6：复杂混合逻辑
     * 规则：(new_account_info.cardNumber != old_account_info.cardNumber 
     *       OR new_account_info.bankName != old_account_info.bankName) 
     *      AND new_account_info.type = 1
     */
    @Test
    public void testRule6_ComplexMixedLogic() {
        // Given
        RuleConditionBO rule = new RuleConditionBO();
        rule.setRuleName("对公账户的卡号或银行名变更规则");

        
        // 创建OR逻辑组
        RuleConditionBO orGroup = new RuleConditionBO();
        orGroup.setLogic(LogicOperatorEnum.OR); // orGroup内部条件使用OR逻辑
        
        RuleConditionBO condition1 = new RuleConditionBO();
        condition1.setFieldPath("new_account_info.cardNumber");
        condition1.setOperator(RuleOperatorEnum.NOT_EQUALS);
        condition1.setCompareFieldPath("old_account_info.cardNumber");
        
        RuleConditionBO condition2 = new RuleConditionBO();
        condition2.setFieldPath("new_account_info.bankName");
        condition2.setOperator(RuleOperatorEnum.NOT_EQUALS);
        condition2.setCompareFieldPath("old_account_info.bankName");
        
        orGroup.setConditions(Arrays.asList(condition1, condition2));
        
        // 添加AND条件
        RuleConditionBO andCondition = new RuleConditionBO();
        andCondition.setFieldPath("new_account_info.type");
        andCondition.setOperator(RuleOperatorEnum.EQUALS);
        andCondition.setCompareValue(1);
        
        rule.setConditions(Arrays.asList(orGroup, andCondition));
        String ruleJson = JSON.toJSONString(rule);
        
        // When
        boolean result = ruleEngine.evaluate(testRecord, ruleJson);
        
        // Then
        assertTrue("(卡号变更 OR 银行名变更) AND 账户类型为1，应该为真", result);
        
        // 测试AND条件为假的情况
        andCondition.setCompareValue(2);
        String ruleJson2 = JSON.toJSONString(rule);
        boolean result2 = ruleEngine.evaluate(testRecord, ruleJson2);
        assertFalse("账户类型为2，整个表达式应该为假", result2);
    }

    /**
     * 测试场景7：三层嵌套逻辑
     * 规则：((new_account_info.cardNumber != old_account_info.cardNumber 
     *       OR new_account_info.bankName != old_account_info.bankName) 
     *      AND new_account_info.type = 1)
     *      OR (new_account_info.holder != old_account_info.holder 
     *          AND new_account_info.openingBank CONTAINS "北京")
     */
    @Test
    public void testRule7_ThreeLevelNestedLogic() {
        // Given
        RuleConditionBO rule = new RuleConditionBO();
        rule.setRuleName("三层嵌套复杂规则");

        // 第一层OR逻辑组
        RuleConditionBO topLevelOrGroup = new RuleConditionBO();
        topLevelOrGroup.setLogic(LogicOperatorEnum.OR);

        // 第二层AND逻辑组1：(卡号变更 OR 银行名变更) AND 账户类型为1
        RuleConditionBO secondLevelAndGroup1 = new RuleConditionBO();
        secondLevelAndGroup1.setLogic(LogicOperatorEnum.AND);

        // 第三层OR逻辑组：卡号变更 OR 银行名变更
        RuleConditionBO thirdLevelOrGroup = new RuleConditionBO();
        thirdLevelOrGroup.setLogic(LogicOperatorEnum.OR);

        RuleConditionBO cardNumberCondition = new RuleConditionBO();
        cardNumberCondition.setFieldPath("new_account_info.cardNumber");
        cardNumberCondition.setOperator(RuleOperatorEnum.NOT_EQUALS);
        cardNumberCondition.setCompareFieldPath("old_account_info.cardNumber");

        RuleConditionBO bankNameCondition = new RuleConditionBO();
        bankNameCondition.setFieldPath("new_account_info.bankName");
        bankNameCondition.setOperator(RuleOperatorEnum.NOT_EQUALS);
        bankNameCondition.setCompareFieldPath("old_account_info.bankName");

        thirdLevelOrGroup.setConditions(Arrays.asList(cardNumberCondition, bankNameCondition));

        // 账户类型条件
        RuleConditionBO accountTypeCondition = new RuleConditionBO();
        accountTypeCondition.setFieldPath("new_account_info.type");
        accountTypeCondition.setOperator(RuleOperatorEnum.EQUALS);
        accountTypeCondition.setCompareValue(1);

        secondLevelAndGroup1.setConditions(Arrays.asList(thirdLevelOrGroup, accountTypeCondition));

        // 第二层AND逻辑组2：持卡人变更 AND 开户行包含"北京"
        RuleConditionBO secondLevelAndGroup2 = new RuleConditionBO();
        secondLevelAndGroup2.setLogic(LogicOperatorEnum.AND);

        RuleConditionBO holderCondition = new RuleConditionBO();
        holderCondition.setFieldPath("new_account_info.holder");
        holderCondition.setOperator(RuleOperatorEnum.NOT_EQUALS);
        holderCondition.setCompareFieldPath("old_account_info.holder");

        RuleConditionBO openingBankCondition = new RuleConditionBO();
        openingBankCondition.setFieldPath("new_account_info.openingBank");
        openingBankCondition.setOperator(RuleOperatorEnum.CONTAINS);
        openingBankCondition.setCompareValue("北京");

        secondLevelAndGroup2.setConditions(Arrays.asList(holderCondition, openingBankCondition));

        // 顶层OR组合
        topLevelOrGroup.setConditions(Arrays.asList(secondLevelAndGroup1, secondLevelAndGroup2));
        rule.setConditions(Arrays.asList(topLevelOrGroup));

        String ruleJson = JSON.toJSONString(rule);
        log.info("三层嵌套规则JSON: {}", ruleJson);

        // When
        boolean result = ruleEngine.evaluate(testRecord, ruleJson);

        // Then
        assertTrue("第一个AND组合应该满足：卡号和银行名都变更且账户类型为1", result);

        // 测试第二个分支：修改测试数据使第一个条件不满足，测试第二个分支
        testRecord.setNewAccountInfo("{"
                + "\"cardNumber\": \"****************\","  // 卡号不变
                + "\"type\": 2,"                          // 类型改为2
                + "\"holder\": \"李四\","                   // 持卡人变更
                + "\"bankName\": \"工商银行\","             // 银行名不变
                + "\"openingBank\": \"工商银行北京分行\","   // 包含"北京"
                + "\"clearingBank\": \"************\""
                + "}");

        boolean result2 = ruleEngine.evaluate(testRecord, ruleJson);
        assertTrue("第二个AND组合应该满足：持卡人变更且开户行包含北京", result2);

        // 测试都不满足的情况
        testRecord.setNewAccountInfo("{"
                + "\"cardNumber\": \"****************\","  // 卡号不变
                + "\"type\": 2,"                          // 类型改为2
                + "\"holder\": \"张三\","                   // 持卡人不变
                + "\"bankName\": \"工商银行\","             // 银行名不变
                + "\"openingBank\": \"工商银行上海分行\","   // 不包含"北京"
                + "\"clearingBank\": \"************\""
                + "}");

        boolean result3 = ruleEngine.evaluate(testRecord, ruleJson);
        assertFalse("两个AND组合都不满足，整体应该为假", result3);
    }

    /**
     * 测试默认规则：空规则JSON时使用默认的卡号变更检查
     */
    @Test
    public void testDefaultRule_CardNumberChange() {
        // When - 传入空字符串，应该使用默认规则
        boolean result1 = ruleEngine.evaluate(testRecord, "");
        boolean result2 = ruleEngine.evaluate(testRecord, null);
        
        // Then
        assertTrue("卡号已变更，默认规则应该为真", result1);
        assertTrue("null规则，默认规则应该为真", result2);
        
        // 测试卡号未变更的情况
        String sameCardNumberNewAccount = "{"
                + "\"cardNumber\": \"****************\","  // 与老卡号相同
                + "\"type\": 2,"
                + "\"holder\": \"李四\","
                + "\"bankName\": \"建设银行\""
                + "}";
        testRecord.setNewAccountInfo(sameCardNumberNewAccount);
        
        boolean result3 = ruleEngine.evaluate(testRecord, "");
        assertFalse("卡号未变更，默认规则应该为假", result3);
    }

    /**
     * 测试NOT_IN操作
     */
    @Test
    public void testNotInOperation() {
        // Given
        RuleConditionBO rule = new RuleConditionBO();
        rule.setRuleName("账户类型黑名单规则");
        
        RuleConditionBO condition = new RuleConditionBO();
        condition.setFieldPath("new_account_info.type");
        condition.setOperator(RuleOperatorEnum.NOT_IN);
        condition.setCompareValueList(Arrays.asList(3, 4));
        
        rule.setConditions(Arrays.asList(condition));
        String ruleJson = JSON.toJSONString(rule);
        
        // When
        boolean result = ruleEngine.evaluate(testRecord, ruleJson);
        
        // Then
        assertTrue("账户类型1不在[3,4]中，NOT_IN应该为真", result);
    }

    /**
     * 测试CONTAINS操作
     */
    @Test
    public void testContainsOperation() {
        // Given
        RuleConditionBO rule = new RuleConditionBO();
        rule.setRuleName("银行名关键字匹配规则");
        
        RuleConditionBO condition = new RuleConditionBO();
        condition.setFieldPath("new_account_info.bankName");
        condition.setOperator(RuleOperatorEnum.CONTAINS);
        condition.setCompareValue("建设");
        
        rule.setConditions(Arrays.asList(condition));
        String ruleJson = JSON.toJSONString(rule);
        
        // When
        boolean result = ruleEngine.evaluate(testRecord, ruleJson);
        
        // Then
        assertTrue("银行名包含'建设'，CONTAINS应该为真", result);
    }

    /**
     * 测试异常情况：JSON解析错误
     */
    @Test
    public void testInvalidJsonRule() {
        // When
        boolean result = ruleEngine.evaluate(testRecord, "{invalid json}");
        
        // Then
        assertTrue("JSON解析错误时应该使用默认规则", result);
    }

    /**
     * 测试异常情况：字段不存在
     */
    @Test
    public void testNonExistentField() {
        // Given
        RuleConditionBO rule = new RuleConditionBO();
        rule.setRuleName("不存在字段检测规则");
        
        RuleConditionBO condition = new RuleConditionBO();
        condition.setFieldPath("new_account_info.nonExistentField");
        condition.setOperator(RuleOperatorEnum.EQUALS);
        condition.setCompareValue("test");
        
        rule.setConditions(Arrays.asList(condition));
        String ruleJson = JSON.toJSONString(rule);
        
        // When
        boolean result = ruleEngine.evaluate(testRecord, ruleJson);
        
        // Then
        assertFalse("不存在的字段与值比较应该为假", result);
    }

    /**
     * 测试规则等价性：嵌套结构 vs 平铺结构的OR逻辑
     * 验证两种不同的JSON结构是否产生相同的评估结果
     */
    @Test
    public void testRuleEquivalence_NestedVsFlatOrLogic() {
        log.info("=== 开始测试规则等价性：嵌套结构 vs 平铺结构 ===");
        
        // ================================
        // 规则1：嵌套结构的OR逻辑
        // ================================
        RuleConditionBO nestedRule = new RuleConditionBO();
        nestedRule.setRuleName("卡号或银行名变更");
        
        // 创建OR逻辑组
        RuleConditionBO orGroup = new RuleConditionBO();
        orGroup.setLogic(LogicOperatorEnum.OR);
        
        RuleConditionBO condition1 = new RuleConditionBO();
        condition1.setFieldPath("new_account_info.cardNumber");
        condition1.setOperator(RuleOperatorEnum.NOT_EQUALS);
        condition1.setCompareFieldPath("old_account_info.cardNumber");
        
        RuleConditionBO condition2 = new RuleConditionBO();
        condition2.setFieldPath("new_account_info.bankName");
        condition2.setOperator(RuleOperatorEnum.NOT_EQUALS);
        condition2.setCompareFieldPath("old_account_info.bankName");
        
        orGroup.setConditions(Arrays.asList(condition1, condition2));
        nestedRule.setConditions(Arrays.asList(orGroup));
        
        String nestedRuleJson = JSON.toJSONString(nestedRule);
        log.info("嵌套结构规则JSON: {}", nestedRuleJson);
        
        // ================================
        // 规则2：平铺结构的OR逻辑
        // ================================
        RuleConditionBO flatRule = new RuleConditionBO();
        flatRule.setRuleName("卡号或银行名变更");
        flatRule.setLogic(LogicOperatorEnum.OR);
        
        RuleConditionBO flatCondition1 = new RuleConditionBO();
        flatCondition1.setFieldPath("new_account_info.cardNumber");
        flatCondition1.setOperator(RuleOperatorEnum.NOT_EQUALS);
        flatCondition1.setCompareFieldPath("old_account_info.cardNumber");
        
        RuleConditionBO flatCondition2 = new RuleConditionBO();
        flatCondition2.setFieldPath("new_account_info.bankName");
        flatCondition2.setOperator(RuleOperatorEnum.NOT_EQUALS);
        flatCondition2.setCompareFieldPath("old_account_info.bankName");
        
        flatRule.setConditions(Arrays.asList(flatCondition1, flatCondition2));
        
        String flatRuleJson = JSON.toJSONString(flatRule);
        log.info("平铺结构规则JSON: {}", flatRuleJson);
        
        // ================================
        // 测试场景1：卡号变更，银行名不变
        // ================================
        log.info("--- 测试场景1：卡号变更，银行名不变 ---");
        testRecord.setNewAccountInfo("{"
                + "\"cardNumber\": \"****************\","  // 卡号变更
                + "\"type\": 1,"
                + "\"holder\": \"张三\","
                + "\"bankName\": \"工商银行\","             // 银行名不变（与old相同）
                + "\"openingBank\": \"工商银行北京分行\","
                + "\"clearingBank\": \"************\""
                + "}");
        
        boolean nestedResult1 = ruleEngine.evaluate(testRecord, nestedRuleJson);
        boolean flatResult1 = ruleEngine.evaluate(testRecord, flatRuleJson);
        
        log.info("嵌套结构结果: {}, 平铺结构结果: {}", nestedResult1, flatResult1);
        assertTrue("卡号变更时，两种结构都应该为真", nestedResult1);
        assertTrue("卡号变更时，两种结构都应该为真", flatResult1);
        assertEquals("场景1：两种结构应该产生相同结果", nestedResult1, flatResult1);
        
        // ================================
        // 测试场景2：卡号不变，银行名变更
        // ================================
        log.info("--- 测试场景2：卡号不变，银行名变更 ---");
        testRecord.setNewAccountInfo("{"
                + "\"cardNumber\": \"****************\","  // 卡号不变（与old相同）
                + "\"type\": 1,"
                + "\"holder\": \"张三\","
                + "\"bankName\": \"建设银行\","             // 银行名变更
                + "\"openingBank\": \"建设银行北京分行\","
                + "\"clearingBank\": \"************\""
                + "}");
        
        boolean nestedResult2 = ruleEngine.evaluate(testRecord, nestedRuleJson);
        boolean flatResult2 = ruleEngine.evaluate(testRecord, flatRuleJson);
        
        log.info("嵌套结构结果: {}, 平铺结构结果: {}", nestedResult2, flatResult2);
        assertTrue("银行名变更时，两种结构都应该为真", nestedResult2);
        assertTrue("银行名变更时，两种结构都应该为真", flatResult2);
        assertEquals("场景2：两种结构应该产生相同结果", nestedResult2, flatResult2);
        
        // ================================
        // 测试场景3：卡号和银行名都变更
        // ================================
        log.info("--- 测试场景3：卡号和银行名都变更 ---");
        testRecord.setNewAccountInfo("{"
                + "\"cardNumber\": \"****************\","  // 卡号变更
                + "\"type\": 1,"
                + "\"holder\": \"张三\","
                + "\"bankName\": \"建设银行\","             // 银行名变更
                + "\"openingBank\": \"建设银行北京分行\","
                + "\"clearingBank\": \"************\""
                + "}");
        
        boolean nestedResult3 = ruleEngine.evaluate(testRecord, nestedRuleJson);
        boolean flatResult3 = ruleEngine.evaluate(testRecord, flatRuleJson);
        
        log.info("嵌套结构结果: {}, 平铺结构结果: {}", nestedResult3, flatResult3);
        assertTrue("卡号和银行名都变更时，两种结构都应该为真", nestedResult3);
        assertTrue("卡号和银行名都变更时，两种结构都应该为真", flatResult3);
        assertEquals("场景3：两种结构应该产生相同结果", nestedResult3, flatResult3);
        
        // ================================
        // 测试场景4：卡号和银行名都不变
        // ================================
        log.info("--- 测试场景4：卡号和银行名都不变 ---");
        testRecord.setNewAccountInfo("{"
                + "\"cardNumber\": \"****************\","  // 卡号不变
                + "\"type\": 2,"                          // 只改类型
                + "\"holder\": \"李四\","                   // 只改持卡人
                + "\"bankName\": \"工商银行\","             // 银行名不变
                + "\"openingBank\": \"工商银行上海分行\","   // 只改开户行
                + "\"clearingBank\": \"************\""
                + "}");
        
        boolean nestedResult4 = ruleEngine.evaluate(testRecord, nestedRuleJson);
        boolean flatResult4 = ruleEngine.evaluate(testRecord, flatRuleJson);
        
        log.info("嵌套结构结果: {}, 平铺结构结果: {}", nestedResult4, flatResult4);
        assertFalse("卡号和银行名都不变时，两种结构都应该为假", nestedResult4);
        assertFalse("卡号和银行名都不变时，两种结构都应该为假", flatResult4);
        assertEquals("场景4：两种结构应该产生相同结果", nestedResult4, flatResult4);
        
        log.info("=== 规则等价性测试完成：所有场景均验证通过 ===");
        
        // ================================
        // 结论验证
        // ================================
        assertTrue("所有测试场景中，两种结构都产生了相同的结果，证明它们是等价的", 
                nestedResult1 == flatResult1 && 
                nestedResult2 == flatResult2 && 
                nestedResult3 == flatResult3 && 
                nestedResult4 == flatResult4);
    }

    /**
     * 测试零限制规则逻辑验证
     * 验证当maxCount=0时，规则引擎的评估逻辑是否正确
     */
    @Test
    public void testZeroLimitRuleValidation() {
        log.info("=== 开始测试零限制规则逻辑 ===");
        
        // 创建禁止卡号变更的零限制规则
        RuleConditionBO zeroLimitRule = new RuleConditionBO();
        zeroLimitRule.setRuleName("禁止卡号变更规则");
        
        RuleConditionBO condition = new RuleConditionBO();
        condition.setFieldPath("new_account_info.cardNumber");
        condition.setOperator(RuleOperatorEnum.NOT_EQUALS);
        condition.setCompareFieldPath("old_account_info.cardNumber");
        
        zeroLimitRule.setConditions(Arrays.asList(condition));
        String ruleJson = JSON.toJSONString(zeroLimitRule);
        
        log.info("零限制规则JSON: {}", ruleJson);
        
        // ================================
        // 测试场景1：卡号变更（应该被规则匹配，禁止变更）
        // ================================
        log.info("--- 测试场景1：卡号变更，应该被禁止 ---");
        testRecord.setNewAccountInfo("{"
                + "\"cardNumber\": \"****************\","  // 卡号变更
                + "\"type\": 1,"
                + "\"holder\": \"张三\","
                + "\"bankName\": \"工商银行\","
                + "\"openingBank\": \"工商银行北京分行\","
                + "\"clearingBank\": \"************\""
                + "}");
        
        boolean result1 = ruleEngine.evaluate(testRecord, ruleJson);
        log.info("卡号变更场景评估结果: {}", result1);
        assertTrue("卡号变更时，规则应该匹配（返回true），表示需要被限制", result1);
        
        // ================================
        // 测试场景2：卡号不变更（不应该被规则匹配，允许变更）
        // ================================
        log.info("--- 测试场景2：卡号不变更，应该被允许 ---");
        testRecord.setNewAccountInfo("{"
                + "\"cardNumber\": \"****************\","  // 卡号不变（与old相同）
                + "\"type\": 2,"                          // 只改类型
                + "\"holder\": \"李四\","                   // 只改持卡人
                + "\"bankName\": \"建设银行\","             // 只改银行
                + "\"openingBank\": \"建设银行北京分行\","
                + "\"clearingBank\": \"************\""
                + "}");
        
        boolean result2 = ruleEngine.evaluate(testRecord, ruleJson);
        log.info("卡号不变更场景评估结果: {}", result2);
        assertFalse("卡号不变更时，规则不应该匹配（返回false），表示不需要被限制", result2);
        
        // ================================
        // 测试场景3：复杂OR规则的零限制场景
        // ================================
        log.info("--- 测试场景3：复杂OR规则零限制场景 ---");
        
        // 创建OR规则：卡号变更 OR 银行名变更
        RuleConditionBO complexZeroLimitRule = new RuleConditionBO();
        complexZeroLimitRule.setRuleName("禁止卡号或银行名变更规则");
        
        RuleConditionBO orGroup = new RuleConditionBO();
        orGroup.setLogic(LogicOperatorEnum.OR);
        
        RuleConditionBO cardCondition = new RuleConditionBO();
        cardCondition.setFieldPath("new_account_info.cardNumber");
        cardCondition.setOperator(RuleOperatorEnum.NOT_EQUALS);
        cardCondition.setCompareFieldPath("old_account_info.cardNumber");
        
        RuleConditionBO bankCondition = new RuleConditionBO();
        bankCondition.setFieldPath("new_account_info.bankName");
        bankCondition.setOperator(RuleOperatorEnum.NOT_EQUALS);
        bankCondition.setCompareFieldPath("old_account_info.bankName");
        
        orGroup.setConditions(Arrays.asList(cardCondition, bankCondition));
        complexZeroLimitRule.setConditions(Arrays.asList(orGroup));
        
        String complexRuleJson = JSON.toJSONString(complexZeroLimitRule);
        log.info("复杂零限制规则JSON: {}", complexRuleJson);
        
        // 测试卡号不变但银行名变更的情况
        testRecord.setNewAccountInfo("{"
                + "\"cardNumber\": \"****************\","  // 卡号不变
                + "\"type\": 1,"
                + "\"holder\": \"张三\","
                + "\"bankName\": \"建设银行\","             // 银行名变更
                + "\"openingBank\": \"建设银行北京分行\","
                + "\"clearingBank\": \"************\""
                + "}");
        
        boolean result3 = ruleEngine.evaluate(testRecord, complexRuleJson);
        log.info("银行名变更场景评估结果: {}", result3);
        assertTrue("银行名变更时，OR规则应该匹配（返回true），表示需要被限制", result3);
        
        // 测试卡号和银行名都不变的情况
        testRecord.setNewAccountInfo("{"
                + "\"cardNumber\": \"****************\","  // 卡号不变
                + "\"type\": 2,"                          // 只改类型
                + "\"holder\": \"李四\","                   // 只改持卡人
                + "\"bankName\": \"工商银行\","             // 银行名不变
                + "\"openingBank\": \"工商银行上海分行\","   // 只改开户行
                + "\"clearingBank\": \"************\""
                + "}");
        
        boolean result4 = ruleEngine.evaluate(testRecord, complexRuleJson);
        log.info("卡号和银行名都不变场景评估结果: {}", result4);
        assertFalse("卡号和银行名都不变时，OR规则不应该匹配（返回false），表示不需要被限制", result4);
        
        log.info("=== 零限制规则逻辑测试完成：所有场景均验证通过 ===");
        
        // ================================
        // 结论说明
        // ================================
        log.info("零限制规则逻辑总结:");
        log.info("- maxCount=0时，规则引擎返回true表示'匹配规则条件，需要被限制'");
        log.info("- maxCount=0时，规则引擎返回false表示'不匹配规则条件，允许变更'");
        log.info("- 这与常规次数限制的逻辑一致：满足条件的记录才会被计数/限制");
    }

    /**
     * 测试AccountChangeValidationResult工厂方法
     * 验证新的静态工厂方法和链式调用是否正常工作
     */
    @Test
    public void testAccountChangeValidationResultFactoryMethods() {
        log.info("=== 开始测试AccountChangeValidationResult工厂方法 ===");
        
        // 测试allowed工厂方法
        AccountChangeValidationResult allowedResult = AccountChangeValidationResult.allowed("允许变更")
                .withRuleInfo(3, 30)
                .withUsedCount(1);
        
        assertTrue("allowed方法应该设置为允许", allowedResult.isAllowed());
        assertEquals("消息应该正确设置", "允许变更", allowedResult.getMessage());
        assertEquals("最大次数应该正确设置", 3, allowedResult.getMaxCount());
        assertEquals("周期天数应该正确设置", 30, allowedResult.getPeriodDays());
        assertEquals("已用次数应该正确设置", 1, allowedResult.getUsedCount());
        
        log.info("✅ allowed工厂方法测试通过");
        
        // 测试blocked工厂方法（不带时间）
        AccountChangeValidationResult blockedResult = AccountChangeValidationResult.blocked("禁止变更")
                .withRuleInfo(0, 30)
                .withUsedCount(0);
        
        assertFalse("blocked方法应该设置为禁止", blockedResult.isAllowed());
        assertEquals("消息应该正确设置", "禁止变更", blockedResult.getMessage());
        assertEquals("最大次数应该正确设置", 0, blockedResult.getMaxCount());
        assertEquals("周期天数应该正确设置", 30, blockedResult.getPeriodDays());
        assertEquals("已用次数应该正确设置", 0, blockedResult.getUsedCount());
        
        log.info("✅ blocked工厂方法（不带时间）测试通过");
        
        // 测试blocked工厂方法（带时间）
        Date nextChangeTime = new Date();
        AccountChangeValidationResult blockedWithTimeResult = AccountChangeValidationResult.blocked("超过限制", nextChangeTime)
                .withRuleInfo(2, 7)
                .withUsedCount(2);
        
        assertFalse("blocked方法应该设置为禁止", blockedWithTimeResult.isAllowed());
        assertEquals("消息应该正确设置", "超过限制", blockedWithTimeResult.getMessage());
        assertEquals("下次变更时间应该正确设置", nextChangeTime, blockedWithTimeResult.getNextChangeTime());
        assertEquals("最大次数应该正确设置", 2, blockedWithTimeResult.getMaxCount());
        assertEquals("周期天数应该正确设置", 7, blockedWithTimeResult.getPeriodDays());
        assertEquals("已用次数应该正确设置", 2, blockedWithTimeResult.getUsedCount());
        
        log.info("✅ blocked工厂方法（带时间）测试通过");
        
        // 测试链式调用的返回值类型
        AccountChangeValidationResult chainResult = AccountChangeValidationResult.allowed("测试链式调用")
                .withRuleInfo(5, 15)
                .withUsedCount(3)
                .withNextChangeTime(new Date());
        
        assertTrue("链式调用应该保持对象类型", chainResult instanceof AccountChangeValidationResult);
        assertEquals("链式调用后所有属性应该正确设置", 5, chainResult.getMaxCount());
        
        log.info("✅ 链式调用测试通过");
        
        log.info("=== AccountChangeValidationResult工厂方法测试完成：所有测试均通过 ===");
        
        // 验证代码简洁性
        log.info("工厂方法优势总结:");
        log.info("- 代码更简洁：一行代码创建结果对象");
        log.info("- 语义更清晰：allowed/blocked明确表达意图");  
        log.info("- 链式调用：支持流畅的属性设置");
        log.info("- 类型安全：编译时检查所有属性类型");
    }
}