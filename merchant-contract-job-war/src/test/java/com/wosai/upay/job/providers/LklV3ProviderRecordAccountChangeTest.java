package com.wosai.upay.job.providers;

import com.wosai.upay.job.dto.BankAccountSimpleInfoBO;
import com.wosai.upay.job.refactor.biz.acquirer.lklv3.LklV3MerchantInfoProcessor;
import com.wosai.upay.job.refactor.task.rotational.RotationalTask;
import com.wosai.upay.job.refactor.task.rotational.builder.AcquirerAccountChangePollingTaskBuilder;
import com.wosai.upay.job.refactor.task.rotational.entity.RotationalTaskContext;
import com.wosai.upay.job.service.AcquirerAccountChangeValidationService;
import com.wosai.upay.merchant.contract.constant.CommonConstant;
import com.wosai.upay.merchant.contract.service.LklV3Service;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.*;

/**
 * LklV3Provider中recordAcquirerAccountChange方法的测试
 * 
 * <AUTHOR>
 * @since 2025-01-22
 */
@Slf4j
@RunWith(MockitoJUnitRunner.class)
public class LklV3ProviderRecordAccountChangeTest {

    @Mock
    private LklV3Service lklV3Service;

    @Mock
    private AcquirerAccountChangeValidationService accountChangeValidationService;

    @Mock
    private LklV3MerchantInfoProcessor lklV3MerchantInfoProcessor;

    @Mock
    private RotationalTask rotationalTask;

    @InjectMocks
    private LklV3Provider lklV3Provider;

    /**
     * 测试脱敏卡号的匹配逻辑
     */
    @Test
    public void testMaskedCardNumberMatching() {
        log.info("=== 测试脱敏卡号匹配逻辑 ===");

        // Given: 正常卡号和脱敏卡号
        String normalCardNumber = "6217000010001234567";
        String maskedCardNumber = "621700*********4567";

        // 模拟LklV3MerchantInfoProcessor的比较方法
        // 这里仅做逻辑验证，实际使用时会调用真实的compareBankCardNos方法
        boolean matches = compareBankCardNosSimple(normalCardNumber, maskedCardNumber);

        // Then: 验证匹配结果
        assertTrue("脱敏卡号应该与正常卡号匹配", matches);

        log.info("✅ 脱敏卡号匹配测试通过, normalCard={}, maskedCard={}", 
                maskCardNumber(normalCardNumber), maskedCardNumber);
    }

    /**
     * 测试不匹配的卡号
     */
    @Test
    public void testNonMatchingCardNumbers() {
        log.info("=== 测试不匹配的卡号 ===");

        String normalCardNumber = "6217000010001234567";
        String differentMaskedCard = "621800*********4567"; // 开头不匹配
        String differentEndMaskedCard = "621700*********4568"; // 结尾不匹配

        assertFalse("开头不匹配的卡号应该返回false", 
                compareBankCardNosSimple(normalCardNumber, differentMaskedCard));
        assertFalse("结尾不匹配的卡号应该返回false", 
                compareBankCardNosSimple(normalCardNumber, differentEndMaskedCard));

        log.info("✅ 不匹配卡号测试通过");
    }

    /**
     * 测试BankAccountCheckInfo对象构建
     */
    @Test
    public void testBuildBankAccountInfo() {
        log.info("=== 测试BankAccountCheckInfo对象构建 ===");

        // Given: 银行账户Map数据
        Map<String, Object> accountMap = new HashMap<>();
        accountMap.put("number", "6217000010001234567");
        accountMap.put("type", 1);
        accountMap.put("holder", "张三");
        accountMap.put("bank_name", "招商银行");
        accountMap.put("branch_name", "招商银行北京分行");
        accountMap.put("clearing_number", "************");

        // When: 构建BankAccountCheckInfo对象（模拟私有方法逻辑）
        BankAccountSimpleInfoBO bankAccount = buildBankAccountInfoSimple(accountMap);

        // Then: 验证构建结果
        assertNotNull("银行账户对象不应为空", bankAccount);
        assertEquals("卡号应匹配", "6217000010001234567", bankAccount.getCardNumber());
        assertEquals("账户类型应匹配", Integer.valueOf(1), bankAccount.getType());
        assertEquals("持卡人应匹配", "张三", bankAccount.getHolder());

        log.info("✅ BankAccountCheckInfo构建测试通过");
    }

    /**
     * 测试空Map的处理
     */
    @Test
    public void testEmptyMapHandling() {
        log.info("=== 测试空Map处理 ===");

        // Given: 空Map
        Map<String, Object> emptyMap = new HashMap<>();
        Map<String, Object> nullMap = null;

        // When: 构建对象
        BankAccountSimpleInfoBO emptyResult = buildBankAccountInfoSimple(emptyMap);
        BankAccountSimpleInfoBO nullResult = buildBankAccountInfoSimple(nullMap);

        // Then: 验证结果
        assertNull("空Map应返回null", emptyResult);
        assertNull("null Map应返回null", nullResult);

        log.info("✅ 空Map处理测试通过");
    }

    /**
     * 测试任务上下文参数构建
     */
    @Test
    public void testContextParameterExtraction() {
        log.info("=== 测试任务上下文参数提取 ===");

        // Given: 模拟contextParam
        Map<String, Object> contextParam = new HashMap<>();
        
        // 商户信息
        Map<String, Object> merchant = new HashMap<>();
        merchant.put("id", "TEST_MERCHANT_ID_001");
        contextParam.put("merchant", merchant);

        // 新银行账户信息
        Map<String, Object> newBankAccount = new HashMap<>();
        newBankAccount.put("number", "6217000010007654321");
        newBankAccount.put("type", 2);
        newBankAccount.put("holder", "李四");
        newBankAccount.put("bank_name", "工商银行");
        contextParam.put(CommonConstant.KEY_BANK_ACCOUNT, newBankAccount);

        // When: 提取参数
        String merchantId = (String) contextParam.get("merchant");
        Map<?, ?> newAccountInfo = (Map<?, ?>) contextParam.get(CommonConstant.KEY_BANK_ACCOUNT);

        // Then: 验证提取结果
        assertNotNull("商户信息不应为空", merchant);
        assertNotNull("新账户信息不应为空", newAccountInfo);
        assertEquals("新账户卡号应匹配", "6217000010007654321", newAccountInfo.get("number"));

        log.info("✅ 上下文参数提取测试通过");
    }

    /**
     * 测试轮询任务构建
     */
    @Test
    public void testPollingTaskBuilder() {
        log.info("=== 测试轮询任务构建 ===");

        // Given: 构建测试数据
        String merchantSn = "TEST_MERCHANT_001";
        Long contractTaskId = 11111L;
        Long contractSubTaskId = 12345L;
        String acquirer = "lklV3";
        String acquirerMerchantId = "LKL_MERCHANT_001";

        BankAccountSimpleInfoBO oldAccount = new BankAccountSimpleInfoBO();
        oldAccount.setCardNumber("6217000010001234567");
        oldAccount.setHolder("张三");
        oldAccount.setType(2);

        BankAccountSimpleInfoBO newAccount = new BankAccountSimpleInfoBO();
        newAccount.setCardNumber("6217000010007654321");
        newAccount.setHolder("张三");
        newAccount.setType(2);

        // When: 构建轮询任务上下文
        RotationalTaskContext taskContext = AcquirerAccountChangePollingTaskBuilder.create()
                .merchantSn(merchantSn)
                .contractTaskId(contractTaskId)
                .contractSubTaskId(contractSubTaskId)
                .acquirer(acquirer)
                .acquirerMerchantId(acquirerMerchantId)
                .belongToContractTask(true)
                .oldAccountInfo(oldAccount)
                .newAccountInfo(newAccount)
                .remark("测试轮询任务")
                .buildContext();

        // Then: 验证构建结果
        assertNotNull("任务上下文不应为空", taskContext);
        assertEquals("商户号应匹配", merchantSn, taskContext.getMerchantSn());
        assertEquals("ContractTaskId应匹配", contractTaskId, taskContext.getContractTaskId());
        assertEquals("ContractSubTaskId应匹配", contractSubTaskId, taskContext.getContractSubTaskId());
        assertTrue("应属于contract任务", taskContext.getBelongToContractTask());
        assertNotNull("轮询ID不应为空", taskContext.getRotationId());

        // 验证参数上下文
        assertNotNull("参数上下文不应为空", taskContext.getParamContext());
        assertEquals("收单机构应匹配", acquirer, 
                taskContext.getParamContextValueByKey("acquirer"));
        assertEquals("收单机构商户ID应匹配", acquirerMerchantId, 
                taskContext.getParamContextValueByKey("acquirerMerchantId"));

        log.info("✅ 轮询任务构建测试通过, rotationId={}", taskContext.getRotationId());
    }

    /**
     * 测试轮询任务JSON序列化
     */
    @Test
    public void testPollingTaskJsonSerialization() {
        log.info("=== 测试轮询任务JSON序列化 ===");

        // Given: 构建测试数据
        BankAccountSimpleInfoBO oldAccount = new BankAccountSimpleInfoBO();
        oldAccount.setCardNumber("6217000010001234567");

        BankAccountSimpleInfoBO newAccount = new BankAccountSimpleInfoBO();
        newAccount.setCardNumber("6217000010007654321");

        // When: 构建并序列化任务
        String taskContextJson = AcquirerAccountChangePollingTaskBuilder.create()
                .merchantSn("TEST_MERCHANT_001")
                .contractTaskId(11111L)
                .contractSubTaskId(12345L)
                .acquirer("lklV3")
                .acquirerMerchantId("LKL_MERCHANT_001")
                .belongToContractTask(true)
                .oldAccountInfo(oldAccount)
                .newAccountInfo(newAccount)
                .remark("JSON序列化测试")
                .build();

        // Then: 验证序列化结果
        assertNotNull("JSON不应为空", taskContextJson);
        assertTrue("JSON应包含商户号", taskContextJson.contains("TEST_MERCHANT_001"));
        assertTrue("JSON应包含收单机构", taskContextJson.contains("lklV3"));
        assertTrue("JSON应包含旧卡号信息", taskContextJson.contains("6217000010001234567"));
        assertTrue("JSON应包含新卡号信息", taskContextJson.contains("6217000010007654321"));

        log.info("✅ 轮询任务JSON序列化测试通过");
        log.debug("Task context JSON: {}", taskContextJson);
    }

    /**
     * 测试轮询任务参数验证
     */
    @Test
    public void testPollingTaskValidation() {
        log.info("=== 测试轮询任务参数验证 ===");

        // Given & When & Then: 测试必要参数缺失
        try {
            AcquirerAccountChangePollingTaskBuilder.create()
                    .acquirer("lklV3")
                    .build(); // 缺少merchantSn
            fail("应抛出参数验证异常");
        } catch (IllegalArgumentException e) {
            assertTrue("异常信息应包含merchantSn", e.getMessage().contains("merchantSn"));
        }

        try {
            AcquirerAccountChangePollingTaskBuilder.create()
                    .merchantSn("TEST_MERCHANT_001")
                    .build(); // 缺少acquirer
            fail("应抛出参数验证异常");
        } catch (IllegalArgumentException e) {
            assertTrue("异常信息应包含acquirer", e.getMessage().contains("acquirer"));
        }

        try {
            AcquirerAccountChangePollingTaskBuilder.create()
                    .merchantSn("TEST_MERCHANT_001")
                    .acquirer("lklV3")
                    .belongToContractTask(true)
                    .build(); // belongToContractTask=true但缺少contractSubTaskId
            fail("应抛出参数验证异常");
        } catch (IllegalArgumentException e) {
            assertTrue("异常信息应包含contractSubTaskId", e.getMessage().contains("contractSubTaskId"));
        }

        log.info("✅ 轮询任务参数验证测试通过");
    }

    /**
     * 测试找不到匹配银行卡的情况
     */
    @Test
    public void testBankAccountNotFound() {
        log.info("=== 测试找不到匹配银行卡的情况 ===");

        // Given: 脱敏卡号和不匹配的银行卡列表
        String maskedCardNumber = "621700*********4567";
        
        // 模拟银行卡记录列表（都不匹配）
        List<Map> records = new ArrayList<>();
        
        Map<String, Object> card1 = new HashMap<>();
        card1.put("number", "6217000010001111111"); // 不匹配
        card1.put("holder", "张三");
        records.add(card1);
        
        Map<String, Object> card2 = new HashMap<>();
        card2.put("number", "6217000010002222222"); // 不匹配
        card2.put("holder", "李四");
        records.add(card2);

        // When: 查找匹配的银行卡
        Map<String, Object> result = findMatchingBankAccountSimple(records, maskedCardNumber);

        // Then: 应该返回null
        assertNull("应该找不到匹配的银行卡", result);

        log.info("✅ 找不到匹配银行卡测试通过");
    }

    /**
     * 测试找到多个匹配银行卡的情况
     */
    @Test
    public void testMultipleBankAccountsFound() {
        log.info("=== 测试找到多个匹配银行卡的情况 ===");

        // Given: 脱敏卡号和多个匹配的银行卡
        String normalCardNumber = "6217000010001234567";
        String maskedCardNumber = "621700*********4567";
        
        List<Map> records = new ArrayList<>();
        
        // 第一个匹配的卡
        Map<String, Object> card1 = new HashMap<>();
        card1.put("number", normalCardNumber);
        card1.put("holder", "张三");
        card1.put("bank_name", "招商银行");
        records.add(card1);
        
        // 第二个匹配的卡（理论上不应该存在，但模拟异常情况）
        Map<String, Object> card2 = new HashMap<>();
        card2.put("number", normalCardNumber);
        card2.put("holder", "张三-副卡");
        card2.put("bank_name", "招商银行");
        records.add(card2);

        // When: 查找匹配的银行卡
        Map<String, Object> result = findMatchingBankAccountSimple(records, maskedCardNumber);

        // Then: 应该返回第一个匹配的卡
        assertNotNull("应该找到匹配的银行卡", result);
        assertEquals("应该返回第一个匹配的卡", "张三", result.get("holder"));

        log.info("✅ 多个匹配银行卡测试通过");
    }

    /**
     * 测试占位符账户信息创建
     */
    @Test
    public void testCreatePlaceholderAccountInfo() {
        log.info("=== 测试占位符账户信息创建 ===");

        // Given: 脱敏卡号
        String maskedCardNumber = "621700*********4567";

        // When: 创建占位符账户信息
        BankAccountSimpleInfoBO placeholder = createPlaceholderAccountInfoSimple(maskedCardNumber);

        // Then: 验证占位符信息
        assertNotNull("占位符账户信息不应为空", placeholder);
        assertEquals("卡号应为脱敏卡号", maskedCardNumber, placeholder.getCardNumber());
        assertEquals("持卡人应为未知", "未知持卡人", placeholder.getHolder());
        assertNull("账户类型应为null", placeholder.getType());

        log.info("✅ 占位符账户信息创建测试通过");
    }

    /**
     * 测试边界情况
     */
    @Test
    public void testEdgeCases() {
        log.info("=== 测试边界情况 ===");

        // Given: 各种边界情况
        String maskedCardNumber = "621700*********4567";

        // When & Then: 空脱敏卡号
        Map<String, Object> result1 = findMatchingBankAccountSimple(new ArrayList<>(), "");
        assertNull("空脱敏卡号应返回null", result1);

        // When & Then: null脱敏卡号
        Map<String, Object> result2 = findMatchingBankAccountSimple(new ArrayList<>(), null);
        assertNull("null脱敏卡号应返回null", result2);

        // When & Then: 空记录列表
        Map<String, Object> result3 = findMatchingBankAccountSimple(new ArrayList<>(), maskedCardNumber);
        assertNull("空记录列表应返回null", result3);

        // When & Then: null记录列表
        Map<String, Object> result4 = findMatchingBankAccountSimple(null, maskedCardNumber);
        assertNull("null记录列表应返回null", result4);

        // When & Then: 记录中卡号为空
        List<Map> recordsWithEmptyCard = new ArrayList<>();
        Map<String, Object> emptyCard = new HashMap<>();
        emptyCard.put("number", "");
        emptyCard.put("holder", "张三");
        recordsWithEmptyCard.add(emptyCard);
        
        Map<String, Object> result5 = findMatchingBankAccountSimple(recordsWithEmptyCard, maskedCardNumber);
        assertNull("空卡号记录应返回null", result5);

        log.info("✅ 边界情况测试通过");
    }

    /**
     * 简化版的卡号比较方法（模拟LklV3MerchantInfoProcessor.compareBankCardNos）
     */
    private boolean compareBankCardNosSimple(String normalCard, String maskedCard) {
        if (normalCard == null || maskedCard == null) {
            return false;
        }
        if (normalCard.length() != maskedCard.length()) {
            return false;
        }
        
        for (int i = 0; i < normalCard.length(); i++) {
            char normalChar = normalCard.charAt(i);
            char maskedChar = maskedCard.charAt(i);
            if (maskedChar != '*' && normalChar != maskedChar) {
                return false;
            }
        }
        return true;
    }

    /**
     * 简化版的BankAccountCheckInfo构建方法（模拟私有方法）
     */
    private BankAccountSimpleInfoBO buildBankAccountInfoSimple(Map<String, Object> accountMap) {
        if (accountMap == null || accountMap.isEmpty()) {
            return null;
        }
        
        BankAccountSimpleInfoBO bankAccount = new BankAccountSimpleInfoBO();
        bankAccount.setCardNumber((String) accountMap.get("number"));
        bankAccount.setType((Integer) accountMap.get("type"));
        bankAccount.setHolder((String) accountMap.get("holder"));

        return bankAccount;
    }

    /**
     * 简化版的查找匹配银行账户方法（模拟私有方法）
     */
    private Map<String, Object> findMatchingBankAccountSimple(List<Map> records, String maskedCardNo) {
        if (maskedCardNo == null || maskedCardNo.isEmpty()) {
            return null;
        }
        
        if (records == null || records.isEmpty()) {
            return null;
        }
        
        for (Map record : records) {
            String cardNumber = (String) record.get("number");
            if (cardNumber != null && !cardNumber.isEmpty()) {
                // 使用简化的比较逻辑
                if (compareBankCardNosSimple(cardNumber, maskedCardNo)) {
                    return record;
                }
            }
        }
        
        return null;
    }

    /**
     * 简化版的占位符账户信息创建方法（模拟私有方法）
     */
    private BankAccountSimpleInfoBO createPlaceholderAccountInfoSimple(String maskedCardNo) {
        BankAccountSimpleInfoBO placeholder = new BankAccountSimpleInfoBO();
        placeholder.setCardNumber(maskedCardNo);
        placeholder.setType(null);
        placeholder.setHolder("未知持卡人");
        return placeholder;
    }

    /**
     * 脱敏卡号用于日志输出（模拟私有方法）
     */
    private String maskCardNumber(String cardNumber) {
        if (cardNumber == null || cardNumber.length() < 8) {
            return cardNumber;
        }
        return cardNumber.substring(0, 4) + "****" + cardNumber.substring(cardNumber.length() - 4);
    }
}
