package com.wosai.upay.job.service;

import com.wosai.trade.service.TradeComboService;
import com.wosai.trade.service.result.QueryTradeComboByIdResult;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

/**
 * TradeComboCache测试类
 * 
 * <AUTHOR>
 */
@RunWith(MockitoJUnitRunner.class)
public class TradeComboCacheTest {

    @Mock
    private TradeComboService tradeComboService;

    @InjectMocks
    private TradeComboCache tradeComboCache;

    private Long testComboId = 123L;
    private String testComboName = "测试套餐";

    @Before
    public void setUp() {
        QueryTradeComboByIdResult result = new QueryTradeComboByIdResult();
        result.setName(testComboName);
        when(tradeComboService.queryTradeComboById(testComboId)).thenReturn(result);
    }

    @Test
    public void testGetTradeComboById_Success() {
        // 第一次调用
        QueryTradeComboByIdResult result1 = tradeComboCache.getTradeComboById(testComboId);
        assertNotNull(result1);
        assertEquals(testComboName, result1.getName());

        // 第二次调用，应该从缓存获取
        QueryTradeComboByIdResult result2 = tradeComboCache.getTradeComboById(testComboId);
        assertNotNull(result2);
        assertEquals(testComboName, result2.getName());

        // 验证只调用了一次远程服务
        verify(tradeComboService, times(1)).queryTradeComboById(testComboId);
    }

    @Test
    public void testGetTradeComboName_Success() {
        String comboName = tradeComboCache.getTradeComboName(testComboId);
        assertEquals(testComboName, comboName);
    }

    @Test
    public void testGetTradeComboName_NullId() {
        String comboName = tradeComboCache.getTradeComboName(null);
        assertEquals("未知套餐", comboName);
    }

    @Test
    public void testGetTradeComboName_ServiceReturnsNull() {
        Long nullComboId = 999L;
        when(tradeComboService.queryTradeComboById(nullComboId)).thenReturn(null);
        
        String comboName = tradeComboCache.getTradeComboName(nullComboId);
        assertEquals("套餐_999", comboName);
    }

    @Test
    public void testEvictCombo() {
        // 先获取一次，放入缓存
        tradeComboCache.getTradeComboById(testComboId);
        verify(tradeComboService, times(1)).queryTradeComboById(testComboId);

        // 清除缓存
        tradeComboCache.evictCombo(testComboId);

        // 再次获取，应该重新调用服务
        tradeComboCache.getTradeComboById(testComboId);
        verify(tradeComboService, times(2)).queryTradeComboById(testComboId);
    }

    @Test
    public void testGetCacheStats() {
        // 调用几次来产生统计数据
        tradeComboCache.getTradeComboById(testComboId);
        tradeComboCache.getTradeComboById(testComboId);
        
        String stats = tradeComboCache.getCacheStats();
        assertNotNull(stats);
        assertTrue(stats.contains("缓存统计"));
    }
}
