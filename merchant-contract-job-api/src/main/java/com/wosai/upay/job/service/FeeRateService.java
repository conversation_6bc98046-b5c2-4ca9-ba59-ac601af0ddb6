package com.wosai.upay.job.service;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.upay.job.dto.IndirectChannelFeeRatePackageDTO;
import org.springframework.validation.annotation.Validated;

import javax.validation.constraints.NotBlank;
import java.util.List;
import java.util.Map;

/**
 *
 * 费率模板相关
 * <AUTHOR>
 */
@JsonRpcService("/rpc/feeRate")
@Validated
public interface FeeRateService{

    /**
     * 根据富友模板获取费率信息
     *
     * @param templateCd   富友模板id
     * @return 枚举映射列表
     */
    String getFeeRateByTemplate(@NotBlank(message = "富友模板不能为空") String templateCd);

    /**
     * 获取间连通道的费率套餐信息
     *
     * @param merchantSn 商户号
     * @return 间连通道费率套餐信息列表
     */
    List<IndirectChannelFeeRatePackageDTO> getIndirectChannelFeeRatePackages(@NotBlank(message = "商户号不能为空") String merchantSn);

}
