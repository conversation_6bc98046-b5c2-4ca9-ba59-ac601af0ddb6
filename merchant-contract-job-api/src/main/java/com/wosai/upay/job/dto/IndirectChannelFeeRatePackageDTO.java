package com.wosai.upay.job.dto;

import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 间连通道费率套餐信息DTO
 * 
 * <AUTHOR>
 */
@Data
public class IndirectChannelFeeRatePackageDTO {

    /**
     * 套餐ID
     */
    private String packageId;

    /**
     * 套餐名称
     */
    private String packageName;

    /**
     * 支付方式
     */
    private Integer payway;

    /**
     * 费率信息
     */
    private FeeRateInfo feeRateInfo;

    /**
     * 费率信息详情
     */
    @Data
    public static class FeeRateInfo {

        /**
         * 基础费率
         */
        private String basicFeeRate;

        /**
         * 渠道费率列表
         */
        private List<Map<String, Object>> channelFeeRates;

        /**
         * 阶梯费率列表
         */
        private List<Map<String, Object>> ladderFeeRates;

        /**
         * 渠道阶梯费率映射
         */
        private Map<String, List<Map<String, Object>>> channelLadderFeeRates;
    }
}
