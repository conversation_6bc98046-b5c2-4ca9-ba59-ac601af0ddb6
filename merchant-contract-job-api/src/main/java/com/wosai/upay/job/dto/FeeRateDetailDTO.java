package com.wosai.upay.job.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 费率详情DTO
 * 
 * <AUTHOR>
 */
@Data
public class FeeRateDetailDTO {

    /**
     * 基础费率
     */
    private String basicFeeRate;

    /**
     * 渠道费率列表
     */
    private List<ChannelFeeRateDTO> channelFeeRates;

    /**
     * 阶梯费率列表
     */
    private List<LadderFeeRateDTO> ladderFeeRates;

    /**
     * 渠道阶梯费率列表
     */
    private List<ChannelLadderFeeRateDTO> channelLadderFeeRates;

    /**
     * 渠道费率DTO
     */
    @Data
    public static class ChannelFeeRateDTO {
        /**
         * 渠道类型
         */
        private String type;
        
        /**
         * 费率值
         */
        private String feeRate;
        
        /**
         * 单笔费用
         */
        private BigDecimal singleFee;
        
        /**
         * 最低费用
         */
        private BigDecimal minFee;
        
        /**
         * 最高费用
         */
        private BigDecimal maxFee;
    }

    /**
     * 阶梯费率DTO
     */
    @Data
    public static class LadderFeeRateDTO {
        /**
         * 阶梯开始金额
         */
        private BigDecimal startAmount;
        
        /**
         * 阶梯结束金额
         */
        private BigDecimal endAmount;
        
        /**
         * 费率值
         */
        private String feeRate;
        
        /**
         * 单笔费用
         */
        private BigDecimal singleFee;
        
        /**
         * 最低费用
         */
        private BigDecimal minFee;
        
        /**
         * 最高费用
         */
        private BigDecimal maxFee;
    }

    /**
     * 渠道阶梯费率DTO
     */
    @Data
    public static class ChannelLadderFeeRateDTO {
        /**
         * 渠道类型
         */
        private String type;
        
        /**
         * 阶梯费率列表
         */
        private List<LadderFeeRateDTO> ladderFeeRates;
    }
}
